#!/bin/bash

# FreelanceDudes Production Deployment Script
# This script automates the deployment process for production servers

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="freelancedudes"
PROJECT_DIR="/var/www/$PROJECT_NAME"
NGINX_CONFIG="/etc/nginx/sites-available/$PROJECT_NAME"
DOMAIN_NAME=""  # Set your domain name here

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        log_info "Please run as a regular user with sudo privileges"
        exit 1
    fi
}

# Update system packages
update_system() {
    log_info "Updating system packages..."
    sudo apt update && sudo apt upgrade -y
    log_success "System packages updated"
}

# Install required packages
install_packages() {
    log_info "Installing required packages..."
    
    # Install Node.js
    if ! command -v node &> /dev/null; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
        log_success "Node.js installed"
    else
        log_info "Node.js already installed"
    fi
    
    # Install MongoDB
    if ! command -v mongod &> /dev/null; then
        wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
        echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
        sudo apt-get update
        sudo apt-get install -y mongodb-org
        sudo systemctl start mongod
        sudo systemctl enable mongod
        log_success "MongoDB installed and started"
    else
        log_info "MongoDB already installed"
    fi
    
    # Install Nginx
    if ! command -v nginx &> /dev/null; then
        sudo apt install nginx -y
        sudo systemctl start nginx
        sudo systemctl enable nginx
        log_success "Nginx installed and started"
    else
        log_info "Nginx already installed"
    fi
    
    # Install PM2
    if ! command -v pm2 &> /dev/null; then
        sudo npm install -g pm2
        log_success "PM2 installed"
    else
        log_info "PM2 already installed"
    fi
    
    # Install other utilities
    sudo apt install -y git curl wget unzip htop
    log_success "Additional packages installed"
}

# Setup project directory
setup_project() {
    log_info "Setting up project directory..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        sudo mkdir -p "$PROJECT_DIR"
        sudo chown -R $USER:$USER "$PROJECT_DIR"
        log_success "Project directory created"
    fi
    
    # Clone or update repository
    if [ ! -d "$PROJECT_DIR/.git" ]; then
        log_info "Cloning repository..."
        # Replace with your actual repository URL
        git clone https://github.com/yourusername/freelancedudes.git "$PROJECT_DIR"
    else
        log_info "Updating repository..."
        cd "$PROJECT_DIR"
        git pull origin main
    fi
    
    cd "$PROJECT_DIR"
    log_success "Project repository ready"
}

# Install dependencies
install_dependencies() {
    log_info "Installing project dependencies..."
    
    cd "$PROJECT_DIR"
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        npm install --production
    fi
    
    # Install backend dependencies
    cd backend
    npm install --production
    
    # Install frontend dependencies and build
    cd ../frontend
    npm install
    npm run build
    
    cd "$PROJECT_DIR"
    log_success "Dependencies installed and frontend built"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment configuration..."
    
    # Backend environment
    if [ ! -f "$PROJECT_DIR/backend/.env" ]; then
        cp "$PROJECT_DIR/backend/.env.example" "$PROJECT_DIR/backend/.env"
        
        # Update environment variables for production
        sed -i "s/NODE_ENV=development/NODE_ENV=production/" "$PROJECT_DIR/backend/.env"
        sed -i "s|CLIENT_URL=http://localhost:3000|CLIENT_URL=https://$DOMAIN_NAME|" "$PROJECT_DIR/backend/.env"
        
        log_warning "Please edit $PROJECT_DIR/backend/.env with your production configuration"
        log_warning "Especially update JWT_SECRET, MONGODB_URI, and other sensitive values"
    fi
    
    log_success "Environment files configured"
}

# Configure Nginx
configure_nginx() {
    log_info "Configuring Nginx..."
    
    if [ -z "$DOMAIN_NAME" ]; then
        log_warning "DOMAIN_NAME not set. Please update the script with your domain name"
        DOMAIN_NAME="your-domain.com"
    fi
    
    sudo tee "$NGINX_CONFIG" > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN_NAME www.$DOMAIN_NAME;

    # Serve React build files
    location / {
        root $PROJECT_DIR/frontend/build;
        index index.html index.htm;
        try_files \$uri \$uri/ /index.html;
    }

    # Proxy API requests to backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Handle file uploads
    location /uploads/ {
        root $PROJECT_DIR/backend/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF
    
    # Enable the site
    sudo ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/
    sudo nginx -t
    sudo systemctl reload nginx
    
    log_success "Nginx configured"
}

# Setup PM2
setup_pm2() {
    log_info "Setting up PM2..."
    
    cd "$PROJECT_DIR/backend"
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js <<EOF
module.exports = {
  apps: [{
    name: '$PROJECT_NAME-api',
    script: 'server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '../logs/err.log',
    out_file: '../logs/out.log',
    log_file: '../logs/combined.log',
    time: true
  }]
};
EOF
    
    # Create logs directory
    mkdir -p "$PROJECT_DIR/logs"
    
    # Start application with PM2
    pm2 start ecosystem.config.js
    pm2 save
    pm2 startup
    
    log_success "PM2 configured and application started"
}

# Setup SSL with Let's Encrypt
setup_ssl() {
    log_info "Setting up SSL certificate..."
    
    if [ -z "$DOMAIN_NAME" ] || [ "$DOMAIN_NAME" = "your-domain.com" ]; then
        log_warning "Domain name not configured. Skipping SSL setup."
        log_info "To setup SSL later, run: sudo certbot --nginx -d yourdomain.com"
        return
    fi
    
    # Install Certbot
    sudo apt install certbot python3-certbot-nginx -y
    
    # Obtain SSL certificate
    sudo certbot --nginx -d "$DOMAIN_NAME" -d "www.$DOMAIN_NAME" --non-interactive --agree-tos --email admin@$DOMAIN_NAME
    
    log_success "SSL certificate configured"
}

# Setup firewall
setup_firewall() {
    log_info "Configuring firewall..."
    
    sudo ufw --force enable
    sudo ufw allow ssh
    sudo ufw allow 'Nginx Full'
    sudo ufw --force reload
    
    log_success "Firewall configured"
}

# Create backup script
create_backup_script() {
    log_info "Creating backup script..."
    
    sudo tee /usr/local/bin/backup-freelancedudes.sh > /dev/null <<EOF
#!/bin/bash
BACKUP_DIR="/var/backups/freelancedudes"
DATE=\$(date +%Y%m%d_%H%M%S)

mkdir -p "\$BACKUP_DIR"

# Backup database
mongodump --db freelancedudes --out "\$BACKUP_DIR/db_\$DATE"

# Backup uploads
tar -czf "\$BACKUP_DIR/uploads_\$DATE.tar.gz" -C "$PROJECT_DIR/backend" uploads

# Keep only last 7 days of backups
find "\$BACKUP_DIR" -type f -mtime +7 -delete
find "\$BACKUP_DIR" -type d -empty -delete

echo "Backup completed: \$DATE"
EOF
    
    sudo chmod +x /usr/local/bin/backup-freelancedudes.sh
    
    # Add to crontab (daily backup at 2 AM)
    (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-freelancedudes.sh") | crontab -
    
    log_success "Backup script created and scheduled"
}

# Main deployment function
main() {
    echo "=================================================="
    echo "    FreelanceDudes Production Deployment"
    echo "=================================================="
    echo ""
    
    check_root
    update_system
    install_packages
    setup_project
    install_dependencies
    setup_environment
    configure_nginx
    setup_pm2
    setup_ssl
    setup_firewall
    create_backup_script
    
    echo ""
    echo "=================================================="
    log_success "Deployment completed successfully!"
    echo "=================================================="
    echo ""
    echo "Your FreelanceDudes platform is now deployed!"
    echo ""
    echo "Next steps:"
    echo "1. Update $PROJECT_DIR/backend/.env with your production configuration"
    echo "2. Restart the application: pm2 restart $PROJECT_NAME-api"
    echo "3. Check application status: pm2 status"
    echo "4. View logs: pm2 logs $PROJECT_NAME-api"
    echo ""
    echo "Your site should be accessible at: http://$DOMAIN_NAME"
    if [ "$DOMAIN_NAME" != "your-domain.com" ]; then
        echo "SSL: https://$DOMAIN_NAME"
    fi
    echo ""
    echo "For monitoring and maintenance:"
    echo "- PM2 monitoring: pm2 monit"
    echo "- Nginx logs: sudo tail -f /var/log/nginx/access.log"
    echo "- Application logs: pm2 logs"
    echo "- Manual backup: /usr/local/bin/backup-freelancedudes.sh"
    echo ""
}

# Run main function
main "$@"
