import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

const HeroSection = styled.section`
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: white;
  padding: 6rem 2rem;
  text-align: center;
`;

const HeroTitle = styled.h1`
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
`;

const HeroSubtitle = styled.p`
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
`;

const Button = styled(Link)`
  display: inline-block;
  background-color: white;
  color: #2563eb;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  text-decoration: none;
  margin: 0 0.5rem;
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-2px);
  }
`;

const SecondaryButton = styled(Button)`
  background-color: transparent;
  border: 2px solid white;
  color: white;
`;

const FeaturesSection = styled.section`
  padding: 6rem 2rem;
  background-color: #f3f4f6;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const FeatureCard = styled.div`
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const FeatureTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1f2937;
`;

const FeatureDescription = styled.p`
  color: #6b7280;
  line-height: 1.6;
`;

const Home = () => {
  return (
    <>
      <HeroSection>
        <HeroTitle>Find Your Next Freelance Opportunity</HeroTitle>
        <HeroSubtitle>
          Connect with top clients and freelancers in one marketplace
        </HeroSubtitle>
        <div>
          <Button to="/register">Get Started</Button>
          <SecondaryButton to="/jobs">Browse Jobs</SecondaryButton>
        </div>
      </HeroSection>

      <FeaturesSection>
        <FeaturesGrid>
          <FeatureCard>
            <FeatureTitle>For Freelancers</FeatureTitle>
            <FeatureDescription>
              Find exciting projects, build your portfolio, and grow your freelance career.
              Get paid securely and on time for your work.
            </FeatureDescription>
          </FeatureCard>

          <FeatureCard>
            <FeatureTitle>For Employers</FeatureTitle>
            <FeatureDescription>
              Post your projects and find skilled freelancers. Manage your team,
              track progress, and make secure payments.
            </FeatureDescription>
          </FeatureCard>

          <FeatureCard>
            <FeatureTitle>Secure Platform</FeatureTitle>
            <FeatureDescription>
              Our escrow system ensures secure payments. Dispute resolution and
              quality assurance for both parties.
            </FeatureDescription>
          </FeatureCard>
        </FeaturesGrid>
      </FeaturesSection>
    </>
  );
};

export default Home; 