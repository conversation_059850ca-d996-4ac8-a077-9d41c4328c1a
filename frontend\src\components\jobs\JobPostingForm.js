import React, { useState } from 'react';
import styled from 'styled-components';
import { Formik, Form, FieldArray } from 'formik';
import * as Yup from 'yup';
import { Card, Button, Input, Select, TextArea, Alert } from '../common';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const FormContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
`;

const FormSection = styled.div`
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || '1fr'};
  gap: 1rem;
  margin-bottom: 1rem;
`;

const SkillTag = styled.div`
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: #eff6ff;
  color: #1d4ed8;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  margin: 0.25rem;
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0;
  
  &:hover {
    color: #dc2626;
  }
`;

const jobCategories = [
  'Web Development',
  'Mobile Development',
  'Design & Creative',
  'Writing & Translation',
  'Digital Marketing',
  'Video & Animation',
  'Music & Audio',
  'Programming & Tech',
  'Business',
  'Data',
  'Photography'
];

const experienceLevels = [
  { value: 'entry', label: 'Entry Level' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'expert', label: 'Expert' }
];

const validationSchema = Yup.object({
  title: Yup.string()
    .required('Job title is required')
    .max(100, 'Title must be less than 100 characters'),
  description: Yup.string()
    .required('Job description is required')
    .min(50, 'Description must be at least 50 characters'),
  category: Yup.string().required('Category is required'),
  jobType: Yup.string().required('Job type is required'),
  experienceLevel: Yup.string().required('Experience level is required'),
  budget: Yup.object().when('jobType', {
    is: 'fixed',
    then: Yup.object({
      fixed: Yup.number()
        .required('Budget is required')
        .min(5, 'Minimum budget is $5')
    }),
    otherwise: Yup.object({
      min: Yup.number()
        .required('Minimum hourly rate is required')
        .min(5, 'Minimum rate is $5'),
      max: Yup.number()
        .required('Maximum hourly rate is required')
        .min(Yup.ref('min'), 'Maximum must be greater than minimum')
    })
  }),
  duration: Yup.object({
    value: Yup.number().required('Duration is required').min(1),
    unit: Yup.string().required('Duration unit is required')
  }),
  skillsRequired: Yup.array().min(1, 'At least one skill is required')
});

const JobPostingForm = ({ initialValues, onSubmit, isEditing = false }) => {
  const { user } = useAuth();
  const [submitError, setSubmitError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const defaultValues = {
    title: '',
    description: '',
    category: '',
    subcategory: '',
    jobType: 'fixed',
    experienceLevel: 'intermediate',
    budget: {
      fixed: '',
      min: '',
      max: ''
    },
    duration: {
      value: '',
      unit: 'weeks'
    },
    skillsRequired: [],
    isRemote: true,
    location: '',
    questions: [],
    visibility: 'public',
    ...initialValues
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitError('');
      setIsSubmitting(true);
      
      const jobData = {
        ...values,
        employer: user.id
      };

      if (isEditing) {
        await axios.put(`/api/v1/jobs/${initialValues._id}`, jobData);
      } else {
        await axios.post('/api/v1/jobs', jobData);
      }

      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      setSubmitError(
        error.response?.data?.message || 
        `Failed to ${isEditing ? 'update' : 'create'} job posting`
      );
    } finally {
      setIsSubmitting(false);
      setSubmitting(false);
    }
  };

  return (
    <FormContainer>
      <Card title={isEditing ? 'Edit Job Posting' : 'Create New Job Posting'}>
        {submitError && (
          <Alert variant="error" style={{ marginBottom: '1rem' }}>
            {submitError}
          </Alert>
        )}

        <Formik
          initialValues={defaultValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, errors, touched, setFieldValue }) => (
            <Form>
              <FormSection>
                <SectionTitle>Job Details</SectionTitle>
                
                <Input
                  name="title"
                  label="Job Title"
                  placeholder="e.g. Build a responsive website"
                  required
                  error={touched.title && errors.title}
                />

                <TextArea
                  name="description"
                  label="Job Description"
                  placeholder="Describe your project in detail..."
                  rows={6}
                  required
                  error={touched.description && errors.description}
                />

                <FormRow columns="1fr 1fr">
                  <Select
                    name="category"
                    label="Category"
                    required
                    error={touched.category && errors.category}
                  >
                    <option value="">Select a category</option>
                    {jobCategories.map(category => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </Select>

                  <Select
                    name="experienceLevel"
                    label="Experience Level"
                    required
                    error={touched.experienceLevel && errors.experienceLevel}
                  >
                    {experienceLevels.map(level => (
                      <option key={level.value} value={level.value}>
                        {level.label}
                      </option>
                    ))}
                  </Select>
                </FormRow>
              </FormSection>

              <FormSection>
                <SectionTitle>Budget & Timeline</SectionTitle>
                
                <FormRow columns="1fr 1fr">
                  <Select
                    name="jobType"
                    label="Job Type"
                    required
                    onChange={(e) => setFieldValue('jobType', e.target.value)}
                  >
                    <option value="fixed">Fixed Price</option>
                    <option value="hourly">Hourly Rate</option>
                  </Select>

                  {values.jobType === 'fixed' ? (
                    <Input
                      name="budget.fixed"
                      label="Fixed Budget ($)"
                      type="number"
                      placeholder="500"
                      required
                      error={touched.budget?.fixed && errors.budget?.fixed}
                    />
                  ) : (
                    <FormRow columns="1fr 1fr">
                      <Input
                        name="budget.min"
                        label="Min Rate ($/hr)"
                        type="number"
                        placeholder="15"
                        required
                        error={touched.budget?.min && errors.budget?.min}
                      />
                      <Input
                        name="budget.max"
                        label="Max Rate ($/hr)"
                        type="number"
                        placeholder="25"
                        required
                        error={touched.budget?.max && errors.budget?.max}
                      />
                    </FormRow>
                  )}
                </FormRow>

                <FormRow columns="1fr 1fr">
                  <Input
                    name="duration.value"
                    label="Project Duration"
                    type="number"
                    placeholder="2"
                    required
                    error={touched.duration?.value && errors.duration?.value}
                  />
                  <Select
                    name="duration.unit"
                    label="Duration Unit"
                    required
                  >
                    <option value="hours">Hours</option>
                    <option value="days">Days</option>
                    <option value="weeks">Weeks</option>
                    <option value="months">Months</option>
                  </Select>
                </FormRow>
              </FormSection>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                <Button variant="secondary" type="button">
                  Save as Draft
                </Button>
                <Button 
                  type="submit" 
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  {isEditing ? 'Update Job' : 'Post Job'}
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </Card>
    </FormContainer>
  );
};

export default JobPostingForm;
