import * as yup from 'yup';

export const authValidation = {
  email: yup.string().email('Invalid email').required('Email is required'),
  password: yup.string()
    .min(8, 'Password must be at least 8 characters')
    .required('Password is required')
};

export const userValidation = {
  name: yup.string().required('Name is required'),
  role: yup.string().oneOf(['freelancer', 'employer']).required('Role is required'),
  ...authValidation
};

export const jobValidation = {
  title: yup.string().required('Title is required'),
  description: yup.string().required('Description is required'),
  budget: yup.number().positive('Budget must be positive').required(),
  skills: yup.array().min(1, 'Select at least one skill')
};

export const proposalValidation = {
  coverLetter: yup.string().required('Cover letter is required'),
  bidAmount: yup.number().positive('Bid amount must be positive').required()
};
