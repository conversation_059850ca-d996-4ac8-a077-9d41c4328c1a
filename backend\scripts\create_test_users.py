from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import User, Base
import bcrypt

def create_test_users():
    # Create database engine
    engine = create_engine('sqlite:///freelance.db')
    Base.metadata.create_all(engine)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Test users data
    test_users = [
        {
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'Admin',
            'last_name': 'User',
            'user_type': 'admin',
            'is_admin': True
        },
        {
            'email': '<EMAIL>',
            'password': 'freelancer123',
            'first_name': '<PERSON>',
            'last_name': 'Freelancer',
            'user_type': 'freelancer',
            'is_admin': False
        },
        {
            'email': '<EMAIL>',
            'password': 'employer123',
            'first_name': '<PERSON>',
            'last_name': 'Employer',
            'user_type': 'employer',
            'is_admin': False
        }
    ]
    
    try:
        # Create each test user
        for user_data in test_users:
            # Check if user already exists
            existing_user = session.query(User).filter_by(email=user_data['email']).first()
            if existing_user:
                print(f"User {user_data['email']} already exists. Skipping...")
                continue
            
            # Hash password
            hashed_password = bcrypt.hashpw(user_data['password'].encode('utf-8'), bcrypt.gensalt())
            
            # Create new user
            new_user = User(
                email=user_data['email'],
                password=hashed_password.decode('utf-8'),
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                user_type=user_data['user_type'],
                is_admin=user_data['is_admin']
            )
            
            session.add(new_user)
            print(f"Created test user: {user_data['email']}")
        
        # Commit changes
        session.commit()
        print("All test users created successfully!")
        
    except Exception as e:
        print(f"Error creating test users: {str(e)}")
        session.rollback()
    finally:
        session.close()

if __name__ == '__main__':
    create_test_users() 