// backend/controllers/authController.js
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { validationResult } = require('express-validator');
const User = require('../models/User');
const FreelancerProfile = require('../models/FreelancerProfile');
const EmployerProfile = require('../models/EmployerProfile');
const emailService = require('../services/emailService');

// Pure functions for password handling
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

const comparePasswords = async (password, hash) => {
  return bcrypt.compare(password, hash);
};

// Pure functions for token generation
const generateToken = (payload) => {
  return new Promise((resolve, reject) => {
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '1d' },
      (err, token) => {
        if (err) reject(err);
        resolve(token);
      }
    );
  });
};

const generateVerificationToken = () => {
  return crypto.randomBytes(20).toString('hex');
};

// Pure functions for user data transformation
const transformUserData = (user) => ({
  id: user.user_id,
  email: user.email,
  first_name: user.first_name,
  last_name: user.last_name,
  user_type: user.user_type,
  profile_image: user.profile_image,
  account_status: user.account_status,
  created_at: user.created_at
});

const createUserPayload = (user) => ({
  user: {
    id: user.user_id,
    email: user.email,
    user_type: user.user_type
  }
});

// Pure functions for validation
const validateRequest = (req) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new Error(JSON.stringify(errors.array()));
  }
  return req.body;
};

// Pure functions for profile creation
const createFreelancerProfile = async (userId, firstName, lastName) => {
  return FreelancerProfile.create({
    user_id: userId,
    title: `${firstName}'s Services`,
    description: `Professional services offered by ${firstName} ${lastName}`
  });
};

const createEmployerProfile = async (userId, companyName) => {
  return EmployerProfile.create({
    user_id: userId,
    company_name: companyName
  });
};

// Controller functions
const register = async (req, res, next) => {
  try {
    const { email, password, first_name, last_name, user_type, company_name } = validateRequest(req);

    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw new Error('User already exists');
    }

    const password_hash = await hashPassword(password);
    const user = await User.create({
      email,
      password_hash,
      first_name,
      last_name,
      user_type
    });

    const verificationToken = generateVerificationToken();
    await User.setVerificationToken(user.user_id, verificationToken);
    await emailService.sendVerificationEmail(email, verificationToken);

    if (user_type === 'freelancer') {
      await createFreelancerProfile(user.user_id, first_name, last_name);
    } else if (user_type === 'employer') {
      await createEmployerProfile(user.user_id, company_name || `${first_name}'s Company`);
    }

    const token = await generateToken(createUserPayload(user));
    res.status(201).json({
      token,
      user: transformUserData(user)
    });
  } catch (err) {
    next(err);
  }
};

const login = async (req, res, next) => {
  try {
    const { email, password } = validateRequest(req);
    const user = await User.findByEmail(email);

    if (!user) {
      throw new Error('Invalid credentials');
    }

    const isMatch = await comparePasswords(password, user.password_hash);
    if (!isMatch) {
      throw new Error('Invalid credentials');
    }

    if (user.account_status !== 'active') {
      throw new Error('Account is not active');
    }

    await User.updateLastLogin(user.user_id);
    const token = await generateToken(createUserPayload(user));

    res.json({
      token,
      user: transformUserData(user)
    });
  } catch (err) {
    next(err);
  }
};

const getCurrentUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      throw new Error('User not found');
    }
    res.json(transformUserData(user));
  } catch (err) {
    next(err);
  }
};

const verifyEmail = async (req, res, next) => {
  try {
    const { token } = req.params;
    const user = await User.findByVerificationToken(token);

    if (!user) {
      throw new Error('Invalid or expired verification token');
    }

    await User.verifyEmail(user.user_id);
    res.json({ message: 'Email verified successfully' });
  } catch (err) {
    next(err);
  }
};

const requestPasswordReset = async (req, res, next) => {
  try {
    const { email } = validateRequest(req);
    const user = await User.findByEmail(email);

    if (user) {
      const resetToken = generateVerificationToken();
      const resetTokenExpiry = Date.now() + 3600000;

      await User.setResetToken(user.user_id, resetToken, resetTokenExpiry);
      await emailService.sendPasswordResetEmail(email, resetToken);
    }

    res.json({ message: 'If your email is registered, you will receive password reset instructions' });
  } catch (err) {
    next(err);
  }
};

const resetPassword = async (req, res, next) => {
  try {
    const { token } = req.params;
    const { password } = validateRequest(req);

    const user = await User.findByResetToken(token);
    if (!user || user.reset_token_expiry < Date.now()) {
      throw new Error('Invalid or expired reset token');
    }

    const password_hash = await hashPassword(password);
    await User.resetPassword(user.user_id, password_hash);

    res.json({ message: 'Password has been reset successfully' });
  } catch (err) {
    next(err);
  }
};

module.exports = {
  register,
  login,
  getCurrentUser,
  verifyEmail,
  requestPasswordReset,
  resetPassword
};