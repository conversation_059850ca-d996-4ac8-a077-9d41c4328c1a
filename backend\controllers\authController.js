// backend/controllers/authController.js
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { validationResult } = require('express-validator');
const User = require('../models/User');
const FreelancerProfile = require('../models/FreelancerProfile');
const EmployerProfile = require('../models/EmployerProfile');
const emailService = require('../services/emailService');

// Pure functions for password handling
const hashPassword = async (password) => {
  const salt = await bcrypt.genSalt(10);
  return bcrypt.hash(password, salt);
};

const comparePasswords = async (password, hash) => {
  return bcrypt.compare(password, hash);
};

// Pure functions for token generation
const generateToken = (payload) => {
  return new Promise((resolve, reject) => {
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '1d' },
      (err, token) => {
        if (err) reject(err);
        resolve(token);
      }
    );
  });
};

const generateVerificationToken = () => {
  return crypto.randomBytes(20).toString('hex');
};

// Pure functions for user data transformation
const transformUserData = (user) => ({
  id: user.user_id,
  email: user.email,
  first_name: user.first_name,
  last_name: user.last_name,
  user_type: user.user_type,
  profile_image: user.profile_image,
  account_status: user.account_status,
  created_at: user.created_at
});

const createUserPayload = (user) => ({
  user: {
    id: user.user_id,
    email: user.email,
    user_type: user.user_type
  }
});

// Pure functions for validation
const validateRequest = (req) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new Error(JSON.stringify(errors.array()));
  }
  return req.body;
};

// Pure functions for profile creation
const createFreelancerProfile = async (userId, firstName, lastName) => {
  return FreelancerProfile.create({
    user_id: userId,
    title: `${firstName}'s Services`,
    description: `Professional services offered by ${firstName} ${lastName}`
  });
};

const createEmployerProfile = async (userId, companyName) => {
  return EmployerProfile.create({
    user_id: userId,
    company_name: companyName
  });
};

// Controller functions
const register = async (req, res, next) => {
  try {
    const { email, password, firstName, lastName, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Create user (password will be hashed by the pre-save middleware)
    const user = await User.create({
      email,
      password,
      firstName,
      lastName,
      role: role || 'user'
    });

    // Create profile based on role
    if (role === 'freelancer') {
      await FreelancerProfile.create({
        user: user._id,
        bio: `Professional services offered by ${firstName} ${lastName}`
      });
    } else if (role === 'employer') {
      await EmployerProfile.create({
        user: user._id,
        companyName: `${firstName}'s Company`
      });
    }

    // Generate JWT token
    const token = user.getSignedJwtToken();

    res.status(201).json({
      success: true,
      token,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      }
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Validate email and password
    if (!email || !password) {
      return res.status(400).json({ message: 'Please provide email and password' });
    }

    // Check for user (include password in select)
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);

    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({ message: 'Account is not active' });
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate JWT token
    const token = user.getSignedJwtToken();

    res.json({
      success: true,
      token,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      }
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getCurrentUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({
      success: true,
      user: {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        avatar: user.avatar,
        isVerified: user.isVerified,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const verifyEmail = async (req, res, next) => {
  try {
    const { token } = req.params;
    const user = await User.findByVerificationToken(token);

    if (!user) {
      throw new Error('Invalid or expired verification token');
    }

    await User.verifyEmail(user.user_id);
    res.json({ message: 'Email verified successfully' });
  } catch (err) {
    next(err);
  }
};

const requestPasswordReset = async (req, res, next) => {
  try {
    const { email } = validateRequest(req);
    const user = await User.findByEmail(email);

    if (user) {
      const resetToken = generateVerificationToken();
      const resetTokenExpiry = Date.now() + 3600000;

      await User.setResetToken(user.user_id, resetToken, resetTokenExpiry);
      await emailService.sendPasswordResetEmail(email, resetToken);
    }

    res.json({ message: 'If your email is registered, you will receive password reset instructions' });
  } catch (err) {
    next(err);
  }
};

const resetPassword = async (req, res, next) => {
  try {
    const { token } = req.params;
    const { password } = validateRequest(req);

    const user = await User.findByResetToken(token);
    if (!user || user.reset_token_expiry < Date.now()) {
      throw new Error('Invalid or expired reset token');
    }

    const password_hash = await hashPassword(password);
    await User.resetPassword(user.user_id, password_hash);

    res.json({ message: 'Password has been reset successfully' });
  } catch (err) {
    next(err);
  }
};

module.exports = {
  register,
  login,
  getCurrentUser,
  verifyEmail,
  requestPasswordReset,
  resetPassword
};