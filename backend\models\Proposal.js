const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ProposalSchema = new Schema({
  job: {
    type: Schema.Types.ObjectId,
    ref: 'Job',
    required: true
  },
  freelancer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  coverLetter: {
    type: String,
    required: [true, 'Please add a cover letter']
  },
  bidAmount: {
    type: Number,
    required: [true, 'Please add a bid amount']
  },
  estimatedTime: {
    value: {
      type: Number,
      required: [true, 'Please add estimated time']
    },
    unit: {
      type: String,
      enum: ['hours', 'days', 'weeks', 'months'],
      required: [true, 'Please add time unit']
    }
  },
  attachments: [{
    name: String,
    url: String
  }],
  status: {
    type: String,
    enum: ['pending', 'accepted', 'rejected', 'withdrawn'],
    default: 'pending'
  },
  isInvitation: {
    type: Boolean,
    default: false
  },
  milestones: [{
    description: String,
    amount: Number,
    dueDate: Date,
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'paid'],
      default: 'pending'
    }
  }],
  questions: [{
    question: String,
    answer: String
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Prevent user from submitting more than one proposal per job
ProposalSchema.index({ job: 1, freelancer: 1 }, { unique: true });

// Update the updatedAt field on save
ProposalSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Proposal', ProposalSchema);
