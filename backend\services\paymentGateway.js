// backend/services/paymentGateway.js
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const db = require('../config/db');
const emailService = require('./emailService');

class PaymentService {
  // Initialize Stripe customer for a user
  async createCustomer(user) {
    try {
      const customer = await stripe.customers.create({
        email: user.email,
        name: `${user.first_name} ${user.last_name}`,
        metadata: {
          user_id: user.user_id
        }
      });
      
      // Store customer ID in user's record
      await db.query(
        'UPDATE users SET stripe_customer_id = $1 WHERE user_id = $2',
        [customer.id, user.user_id]
      );
      
      return customer.id;
    } catch (error) {
      console.error('Error creating Stripe customer:', error);
      throw error;
    }
  }
  
  // Get or create Stripe customer
  async getCustomer(userId) {
    try {
      // Check if user already has a Stripe customer ID
      const { rows } = await db.query(
        'SELECT stripe_customer_id FROM users WHERE user_id = $1',
        [userId]
      );
      
      if (rows.length > 0 && rows[0].stripe_customer_id) {
        return rows[0].stripe_customer_id;
      }
      
      // If not, get user data and create a customer
      const userResult = await db.query(
        'SELECT * FROM users WHERE user_id = $1',
        [userId]
      );
      
      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }
      
      return await this.createCustomer(userResult.rows[0]);
    } catch (error) {
      console.error('Error getting/creating Stripe customer:', error);
      throw error;
    }
  }
  
  // Process payment for a milestone or time log
  async processPayment(paymentData) {
    const {
      payer_user_id,
      payee_user_id,
      contract_id,
      milestone_id,
      time_log_id,
      amount,
      description
    } = paymentData;
    
    try {
      return await db.transaction(async (client) => {
        // Get platform fee percentage from settings
        const settingsResult = await client.query(
          "SELECT setting_value FROM admin_settings WHERE setting_key = 'platform_fee_percentage'"
        );
        
        const platformFeePercentage = settingsResult.rows.length > 0 
          ? parseFloat(settingsResult.rows[0].setting_value) 
          : 10; // Default 10%
        
        const platformFee = (amount * platformFeePercentage / 100).toFixed(2);
        const payeeAmount = (amount - platformFee).toFixed(2);
        
        // Get Stripe customer ID for payer
        const customerResult = await client.query(
          'SELECT stripe_customer_id FROM users WHERE user_id = $1',
          [payer_user_id]
        );
        
        if (!customerResult.rows[0]?.stripe_customer_id) {
          throw new Error('Payer does not have a payment method set up');
        }
        
        const customerId = customerResult.rows[0].stripe_customer_id;
        
        // Create payment method if not exists
        let paymentMethodId;
        
        try {
          // Check if customer has a default payment method
          const paymentMethods = await stripe.paymentMethods.list({
            customer: customerId,
            type: 'card',
          });
          
          if (paymentMethods.data.length > 0) {
            paymentMethodId = paymentMethods.data[0].id;
          } else {
            throw new Error('No payment method found for customer');
          }
        } catch (error) {
          console.error('Error getting payment method:', error);
          throw new Error('Failed to retrieve payment method');
        }
        
        // Create payment intent
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount * 100), // Convert to cents
          currency: 'usd',
          customer: customerId,
          payment_method: paymentMethodId,
          confirm: true,
          description: description || `Payment for contract #${contract_id}`,
          metadata: {
            contract_id,
            milestone_id: milestone_id || null,
            time_log_id: time_log_id || null,
            payer_user_id,
            payee_user_id
          }
        });
        
        // Create payment record in database
        const { rows } = await client.query(
          `INSERT INTO payments 
           (payer_user_id, payee_user_id, contract_id, milestone_id, time_log_id, 
            amount, payment_method, transaction_fee, platform_fee, status, 
            payment_date, external_transaction_id)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), $11)
           RETURNING *`,
          [
            payer_user_id,
            payee_user_id,
            contract_id,
            milestone_id,
            time_log_id,
            amount,
            'credit_card',
            0, // Transaction fee is absorbed by platform
            platformFee,
            'completed',
            paymentIntent.id
          ]
        );
        
        const payment = rows[0];
        
        // Update milestone or time log status
        if (milestone_id) {
          await client.query(
            `UPDATE milestones SET status = 'paid' WHERE milestone_id = $1`,
            [milestone_id]
          );
        } else if (time_log_id) {
          await client.query(
            `UPDATE time_logs SET status = 'paid' WHERE log_id = $1`,
            [time_log_id]
          );
        }
        
        // Send notification emails
        await emailService.sendPaymentReceivedEmail(
          payee_user_id, 
          payeeAmount, 
          contract_id
        );
        
        await emailService.sendPaymentConfirmationEmail(
          payer_user_id, 
          amount, 
          contract_id
        );
        
        return payment;
      });
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }
  
  // Create an escrow payment
  async createEscrowPayment(paymentData) {
    const {
      payer_user_id,
      payee_user_id,
      contract_id,
      milestone_id,
      amount,
      description
    } = paymentData;
    
    try {
      return await db.transaction(async (client) => {
        // Get platform fee percentage from settings
        const settingsResult = await client.query(
          "SELECT setting_value FROM admin_settings WHERE setting_key = 'platform_fee_percentage'"
        );
        
        const platformFeePercentage = settingsResult.rows.length > 0 
          ? parseFloat(settingsResult.rows[0].setting_value) 
          : 10; // Default 10%
        
        const platformFee = (amount * platformFeePercentage / 100).toFixed(2);
        
        // Get Stripe customer ID for payer
        const customerResult = await client.query(
          'SELECT stripe_customer_id FROM users WHERE user_id = $1',
          [payer_user_id]
        );
        
        if (!customerResult.rows[0]?.stripe_customer_id) {
          throw new Error('Payer does not have a payment method set up');
        }
        
        const customerId = customerResult.rows[0].stripe_customer_id;
        
        // Get or create payment method
        let paymentMethodId;
        
        try {
          // Check if customer has a default payment method
          const paymentMethods = await stripe.paymentMethods.list({
            customer: customerId,
            type: 'card',
          });
          
          if (paymentMethods.data.length > 0) {
            paymentMethodId = paymentMethods.data[0].id;
          } else {
            throw new Error('No payment method found for customer');
          }
        } catch (error) {
          console.error('Error getting payment method:', error);
          throw new Error('Failed to retrieve payment method');
        }
        
        // Create payment intent but hold in escrow
        const paymentIntent = await stripe.paymentIntents.create({
          amount: Math.round(amount * 100), // Convert to cents
          currency: 'usd',
          customer: customerId,
          payment_method: paymentMethodId,
          confirm: true,
          description: description || `Escrow payment for milestone #${milestone_id}`,
          metadata: {
            contract_id,
            milestone_id,
            payer_user_id,
            payee_user_id,
            is_escrow: true
          }
        });
        
        // Create payment record in database with 'escrow' status
        const { rows } = await client.query(
          `INSERT INTO payments 
           (payer_user_id, payee_user_id, contract_id, milestone_id, 
            amount, payment_method, transaction_fee, platform_fee, status, 
            payment_date, external_transaction_id)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
           RETURNING *`,
          [
            payer_user_id,
            payee_user_id,
            contract_id,
            milestone_id,
            amount,
            'credit_card',
            0, // Transaction fee is absorbed by platform
            platformFee,
            'escrow', // Held in escrow
            paymentIntent.id
          ]
        );
        
        const payment = rows[0];
        
        // Update milestone status to 'funded'
        await client.query(
          `UPDATE milestones SET status = 'funded' WHERE milestone_id = $1`,
          [milestone_id]
        );
        
        // Send notification emails
        await emailService.sendMilestoneFundedEmail(
          payee_user_id, 
          amount, 
          contract_id,
          milestone_id
        );
        
        await emailService.sendFundingConfirmationEmail(
          payer_user_id, 
          amount, 
          contract_id,
          milestone_id
        );
        
        return payment;
      });
    } catch (error) {
      console.error('Error creating escrow payment:', error);
      throw error;
    }
  }
  
  // Release payment from escrow
  async releaseEscrowPayment(paymentId, releasedBy) {
    try {
      return await db.transaction(async (client) => {
        // Get payment details
        const paymentResult = await client.query(
          'SELECT * FROM payments WHERE payment_id = $1 AND status = $2',
          [paymentId, 'escrow']
        );
        
        if (paymentResult.rows.length === 0) {
          throw new Error('Escrow payment not found or already released');
        }
        
        const payment = paymentResult.rows[0];
        
        // Check authorization
        if (releasedBy !== payment.payer_user_id && !await this.isAdmin(releasedBy, client)) {
          throw new Error('Unauthorized to release this payment');
        }
        
        // Update payment status
        await client.query(
          'UPDATE payments SET status = $1, updated_at = NOW() WHERE payment_id = $2',
          ['completed', paymentId]
        );
        
        // Update milestone status
        if (payment.milestone_id) {
          await client.query(
            'UPDATE milestones SET status = $1 WHERE milestone_id = $2',
            ['paid', payment.milestone_id]
          );
        }
        
        // Send notification emails
        await emailService.sendPaymentReleasedEmail(
          payment.payee_user_id,
          payment.amount - payment.platform_fee,
          payment.contract_id
        );
        
        await emailService.sendReleaseConfirmationEmail(
          payment.payer_user_id,
          payment.amount,
          payment.contract_id
        );
        
        return { ...payment, status: 'completed' };
      });
    } catch (error) {
      console.error('Error releasing escrow payment:', error);
      throw error;
    }
  }
  
  // Request refund for a payment
  async requestRefund(paymentId, requestedBy, reason) {
    try {
      return await db.transaction(async (client) => {
        // Get payment details
        const paymentResult = await client.query(
          'SELECT * FROM payments WHERE payment_id = $1 AND status IN ($2, $3)',
          [paymentId, 'completed', 'escrow']
        );
        
        if (paymentResult.rows.length === 0) {
          throw new Error('Payment not found or not eligible for refund');
        }
        
        const payment = paymentResult.rows[0];
        
        // Check authorization
        if (requestedBy !== payment.payer_user_id && !await this.isAdmin(requestedBy, client)) {
          throw new Error('Unauthorized to request refund for this payment');
        }
        
        // Update payment status
        await client.query(
          'UPDATE payments SET status = $1, updated_at = NOW(), refund_reason = $2 WHERE payment_id = $3',
          ['refund_requested', reason, paymentId]
        );
        
        // Create dispute record
        await client.query(
          `INSERT INTO disputes
           (payment_id, contract_id, initiator_id, respondent_id, reason, status)
           VALUES ($1, $2, $3, $4, $5, $6)`,
          [
            paymentId,
            payment.contract_id,
            requestedBy,
            payment.payee_user_id,
            reason,
            'open'
          ]
        );
        
        // Send notification emails
        await emailService.sendRefundRequestedEmail(
          payment.payee_user_id,
          payment.amount - payment.platform_fee,
          payment.contract_id,
          reason
        );
        
        await emailService.sendRefundRequestConfirmationEmail(
          payment.payer_user_id,
          payment.amount,
          payment.contract_id
        );
        
        return { ...payment, status: 'refund_requested' };
      });
    } catch (error) {
      console.error('Error requesting refund:', error);
      throw error;
    }
  }
  
  // Process refund
  async processRefund(paymentId, processedBy) {
    try {
      return await db.transaction(async (client) => {
        // Get payment details
        const paymentResult = await client.query(
          'SELECT * FROM payments WHERE payment_id = $1 AND status = $2',
          [paymentId, 'refund_requested']
        );
        
        if (paymentResult.rows.length === 0) {
          throw new Error('Payment not found or not eligible for refund');
        }
        
        const payment = paymentResult.rows[0];
        
        // Check authorization
        if (!await this.isAdmin(processedBy, client)) {
          throw new Error('Only administrators can process refunds');
        }
        
        // Refund via Stripe
        const refund = await stripe.refunds.create({
          payment_intent: payment.external_transaction_id,
          reason: 'requested_by_customer',
        });
        
        // Update payment status
        await client.query(
          'UPDATE payments SET status = $1, updated_at = NOW(), refund_transaction_id = $2 WHERE payment_id = $3',
          ['refunded', refund.id, paymentId]
        );
        
        // Update dispute status
        await client.query(
          'UPDATE disputes SET status = $1, resolved_at = NOW(), resolver_id = $2 WHERE payment_id = $3',
          ['resolved_with_refund', processedBy, paymentId]
        );
        
        // Send notification emails
        await emailService.sendRefundProcessedEmail(
          payment.payee_user_id,
          payment.amount - payment.platform_fee,
          payment.contract_id
        );
        
        await emailService.sendRefundConfirmationEmail(
          payment.payer_user_id,
          payment.amount,
          payment.contract_id
        );
        
        return { ...payment, status: 'refunded' };
      });
    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  }
  
  // Get payment history for a user
  async getPaymentHistory(userId, filters = {}, page = 1, limit = 10) {
    try {
      const offset = (page - 1) * limit;
      let query = `
        SELECT p.*, 
               c.title as contract_title,
               m.title as milestone_title,
               tl.description as time_log_description,
               u_payer.first_name as payer_first_name,
               u_payer.last_name as payer_last_name,
               u_payee.first_name as payee_first_name,
               u_payee.last_name as payee_last_name
        FROM payments p
        LEFT JOIN contracts c ON p.contract_id = c.contract_id
        LEFT JOIN milestones m ON p.milestone_id = m.milestone_id
        LEFT JOIN time_logs tl ON p.time_log_id = tl.log_id
        LEFT JOIN users u_payer ON p.payer_user_id = u_payer.user_id
        LEFT JOIN users u_payee ON p.payee_user_id = u_payee.user_id
        WHERE (p.payer_user_id = $1 OR p.payee_user_id = $1)
      `;
      
      let countQuery = `
        SELECT COUNT(*) 
        FROM payments
        WHERE (payer_user_id = $1 OR payee_user_id = $1)
      `;
      
      const queryParams = [userId];
      let paramCounter = 2;
      
      // Apply filters
      if (filters.status) {
        query += ` AND p.status = $${paramCounter}`;
        countQuery += ` AND status = $${paramCounter}`;
        queryParams.push(filters.status);
        paramCounter++;
      }
      
      if (filters.type) {
        if (filters.type === 'sent') {
          query += ` AND p.payer_user_id = $1`;
          countQuery += ` AND payer_user_id = $1`;
        } else if (filters.type === 'received') {
          query += ` AND p.payee_user_id = $1`;
          countQuery += ` AND payee_user_id = $1`;
        }
      }
      
      if (filters.contract_id) {
        query += ` AND p.contract_id = $${paramCounter}`;
        countQuery += ` AND contract_id = $${paramCounter}`;
        queryParams.push(filters.contract_id);
        paramCounter++;
      }
      
      if (filters.date_from) {
        query += ` AND p.payment_date >= $${paramCounter}`;
        countQuery += ` AND payment_date >= $${paramCounter}`;
        queryParams.push(filters.date_from);
        paramCounter++;
      }
      
      if (filters.date_to) {
        query += ` AND p.payment_date <= $${paramCounter}`;
        countQuery += ` AND payment_date <= $${paramCounter}`;
        queryParams.push(filters.date_to);
        paramCounter++;
      }
      
      // Add ordering and pagination
      query += ' ORDER BY p.payment_date DESC LIMIT $' + paramCounter + ' OFFSET $' + (paramCounter + 1);
      queryParams.push(limit, offset);
      
      // Execute queries
      const { rows } = await db.query(query, queryParams);
      const countResult = await db.query(countQuery, queryParams.slice(0, paramCounter - 1));
      
      return {
        payments: rows,
        total: parseInt(countResult.rows[0].count),
        page,
        limit,
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      };
    } catch (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }
  }
  
  // Check if a user is an admin
  async isAdmin(userId, client) {
    const dbClient = client || db;
    
    try {
      const { rows } = await dbClient.query(
        'SELECT user_type FROM users WHERE user_id = $1',
        [userId]
      );
      
      return rows.length > 0 && rows[0].user_type === 'admin';
    } catch (error) {
      console.error('Error checking admin status:', error);
      throw error;
    }
  }
  
  // Add a payment method for a user
  async addPaymentMethod(userId, paymentMethodData) {
    try {
      const customerId = await this.getCustomer(userId);
      
      const { card_number, exp_month, exp_year, cvc } = paymentMethodData;
      
      // Create a payment method
      const paymentMethod = await stripe.paymentMethods.create({
        type: 'card',
        card: {
          number: card_number,
          exp_month,
          exp_year,
          cvc,
        },
      });
      
      // Attach payment method to customer
      await stripe.paymentMethods.attach(paymentMethod.id, {
        customer: customerId,
      });
      
      // Set as default payment method
      await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethod.id,
        },
      });
      
      return {
        id: paymentMethod.id,
        brand: paymentMethod.card.brand,
        last4: paymentMethod.card.last4,
        exp_month: paymentMethod.card.exp_month,
        exp_year: paymentMethod.card.exp_year
      };
    } catch (error) {
      console.error('Error adding payment method:', error);
      throw error;
    }
  }
  
  // Get payment methods for a user
  async getPaymentMethods(userId) {
    try {
      const customerId = await this.getCustomer(userId);
      
      const paymentMethods = await stripe.paymentMethods.list({
        customer: customerId,
        type: 'card',
      });
      
      return paymentMethods.data.map(pm => ({
        id: pm.id,
        brand: pm.card.brand,
        last4: pm.card.last4,
        exp_month: pm.card.exp_month,
        exp_year: pm.card.exp_year
      }));
    } catch (error) {
      console.error('Error getting payment methods:', error);
      throw error;
    }
  }
}

module.exports = new PaymentService();