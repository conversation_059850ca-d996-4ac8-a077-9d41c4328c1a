const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ConversationSchema = new Schema({
  participants: [{
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  job: {
    type: Schema.Types.ObjectId,
    ref: 'Job'
  },
  contract: {
    type: Schema.Types.ObjectId,
    ref: 'Contract'
  },
  lastMessage: {
    type: Schema.Types.ObjectId,
    ref: 'Message'
  },
  unreadCount: {
    type: Number,
    default: 0
  },
  isArchived: [{
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User'
    },
    archived: {
      type: Boolean,
      default: false
    }
  }],
  isBlocked: {
    type: Boolean,
    default: false
  },
  blockedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Ensure each participant pair has only one conversation per job/contract
ConversationSchema.index({ participants: 1, job: 1 }, { unique: true, partialFilterExpression: { job: { $exists: true } } });
ConversationSchema.index({ participants: 1, contract: 1 }, { unique: true, partialFilterExpression: { contract: { $exists: true } } });

// Update the updatedAt field on save
ConversationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Conversation', ConversationSchema);
