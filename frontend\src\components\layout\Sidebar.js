import React from 'react';
import styled from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const SidebarContainer = styled.aside`
  width: 250px;
  background-color: #ffffff;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const UserInfo = styled.div`
  padding-bottom: 2rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 2rem;
`;

const UserName = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
`;

const UserType = styled.span`
  color: #6b7280;
  font-size: 0.875rem;
`;

const NavList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const NavItem = styled.li`
  margin-bottom: 0.5rem;
`;

const NavLink = styled(Link)`
  display: flex;
  align-items: center;
  padding: 0.75rem;
  color: ${props => props.active ? '#2563eb' : '#4b5563'};
  text-decoration: none;
  border-radius: 0.375rem;
  font-weight: 500;
  background-color: ${props => props.active ? '#eff6ff' : 'transparent'};
  
  &:hover {
    background-color: #f3f4f6;
    color: #2563eb;
  }
`;

const Sidebar = () => {
  const { user } = useAuth();
  const location = useLocation();

  const isActive = (path) => location.pathname === path;

  const getNavLinks = () => {
    if (!user) return [];

    if (user.user_type === 'freelancer') {
      return [
        { path: '/dashboard', label: 'Dashboard' },
        { path: '/jobs', label: 'Find Jobs' },
        { path: '/proposals', label: 'My Proposals' },
        { path: '/contracts', label: 'Contracts' },
        { path: '/earnings', label: 'Earnings' },
        { path: '/profile', label: 'Profile' },
        { path: '/settings', label: 'Settings' }
      ];
    } else if (user.user_type === 'employer') {
      return [
        { path: '/dashboard', label: 'Dashboard' },
        { path: '/post-job', label: 'Post a Job' },
        { path: '/my-jobs', label: 'My Jobs' },
        { path: '/contracts', label: 'Contracts' },
        { path: '/payments', label: 'Payments' },
        { path: '/profile', label: 'Profile' },
        { path: '/settings', label: 'Settings' }
      ];
    } else if (user.user_type === 'admin') {
      return [
        { path: '/dashboard', label: 'Dashboard' },
        { path: '/users', label: 'Users' },
        { path: '/jobs', label: 'Jobs' },
        { path: '/transactions', label: 'Transactions' },
        { path: '/reports', label: 'Reports' },
        { path: '/settings', label: 'Settings' }
      ];
    }

    return [];
  };

  return (
    <SidebarContainer>
      {user && (
        <UserInfo>
          <UserName>{user.first_name} {user.last_name}</UserName>
          <UserType>{user.user_type.charAt(0).toUpperCase() + user.user_type.slice(1)}</UserType>
        </UserInfo>
      )}
      <NavList>
        {getNavLinks().map((link) => (
          <NavItem key={link.path}>
            <NavLink to={link.path} active={isActive(link.path)}>
              {link.label}
            </NavLink>
          </NavItem>
        ))}
      </NavList>
    </SidebarContainer>
  );
};

export default Sidebar; 