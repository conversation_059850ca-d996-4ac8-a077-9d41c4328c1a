import React, { useState } from 'react';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { Card, Button, Input, TextArea, Alert, Select } from '../common';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const FormContainer = styled.div`
  max-width: 700px;
  margin: 0 auto;
  padding: 2rem;
`;

const FormSection = styled.div`
  margin-bottom: 2rem;
`;

const SectionTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 1rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns || '1fr'};
  gap: 1rem;
  margin-bottom: 1rem;
`;

const JobSummary = styled.div`
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 2rem;
`;

const JobTitle = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const JobDetails = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
`;

const MilestoneContainer = styled.div`
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
  background-color: #fafafa;
`;

const MilestoneHeader = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 1rem;
`;

const AddMilestoneButton = styled(Button)`
  margin-top: 1rem;
`;

const FileUploadArea = styled.div`
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: #2563eb;
    background-color: #f8fafc;
  }
`;

const validationSchema = Yup.object({
  coverLetter: Yup.string()
    .required('Cover letter is required')
    .min(100, 'Cover letter must be at least 100 characters'),
  bidAmount: Yup.number()
    .required('Bid amount is required')
    .min(5, 'Minimum bid is $5'),
  estimatedTime: Yup.object({
    value: Yup.number()
      .required('Estimated time is required')
      .min(1, 'Minimum time is 1'),
    unit: Yup.string().required('Time unit is required')
  })
});

const ProposalForm = ({ job, onSubmit, onCancel }) => {
  const { user } = useAuth();
  const [submitError, setSubmitError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [milestones, setMilestones] = useState([]);

  const initialValues = {
    coverLetter: '',
    bidAmount: job?.jobType === 'fixed' ? job?.budget?.fixed || '' : '',
    estimatedTime: {
      value: '',
      unit: 'weeks'
    },
    attachments: [],
    questions: job?.questions?.map(q => ({ question: q.question, answer: '' })) || []
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    try {
      setSubmitError('');
      setIsSubmitting(true);
      
      const proposalData = {
        ...values,
        job: job._id,
        freelancer: user.id,
        milestones: milestones
      };

      const response = await axios.post('/api/v1/proposals', proposalData);
      
      if (onSubmit) {
        onSubmit(response.data);
      }
    } catch (error) {
      setSubmitError(
        error.response?.data?.message || 
        'Failed to submit proposal. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
      setSubmitting(false);
    }
  };

  const addMilestone = () => {
    setMilestones([
      ...milestones,
      {
        description: '',
        amount: '',
        dueDate: ''
      }
    ]);
  };

  const removeMilestone = (index) => {
    setMilestones(milestones.filter((_, i) => i !== index));
  };

  const updateMilestone = (index, field, value) => {
    const updated = [...milestones];
    updated[index][field] = value;
    setMilestones(updated);
  };

  return (
    <FormContainer>
      <Card title="Submit Proposal">
        {/* Job Summary */}
        <JobSummary>
          <JobTitle>{job?.title}</JobTitle>
          <JobDetails>
            <span>{job?.jobType === 'fixed' ? 'Fixed Price' : 'Hourly'}</span>
            <span>•</span>
            <span>
              {job?.jobType === 'fixed' 
                ? `$${job?.budget?.fixed}` 
                : `$${job?.budget?.min}-$${job?.budget?.max}/hr`
              }
            </span>
            <span>•</span>
            <span>{job?.experienceLevel} Level</span>
            <span>•</span>
            <span>Posted by {job?.employer?.firstName} {job?.employer?.lastName}</span>
          </JobDetails>
        </JobSummary>

        {submitError && (
          <Alert variant="error" style={{ marginBottom: '1rem' }}>
            {submitError}
          </Alert>
        )}

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, errors, touched, setFieldValue }) => (
            <Form>
              <FormSection>
                <SectionTitle>Cover Letter</SectionTitle>
                <TextArea
                  name="coverLetter"
                  placeholder="Explain why you're the best fit for this project..."
                  rows={8}
                  maxLength={2000}
                  showCharacterCount
                  required
                  error={touched.coverLetter && errors.coverLetter}
                />
              </FormSection>

              <FormSection>
                <SectionTitle>Bid Details</SectionTitle>
                
                <FormRow columns="1fr 1fr">
                  <Input
                    name="bidAmount"
                    label={job?.jobType === 'fixed' ? 'Total Bid Amount ($)' : 'Hourly Rate ($)'}
                    type="number"
                    placeholder={job?.jobType === 'fixed' ? '500' : '25'}
                    required
                    error={touched.bidAmount && errors.bidAmount}
                  />
                  
                  <div>
                    <FormRow columns="2fr 1fr">
                      <Input
                        name="estimatedTime.value"
                        label="Estimated Time"
                        type="number"
                        placeholder="2"
                        required
                        error={touched.estimatedTime?.value && errors.estimatedTime?.value}
                      />
                      <Select
                        name="estimatedTime.unit"
                        label="Unit"
                        required
                      >
                        <option value="hours">Hours</option>
                        <option value="days">Days</option>
                        <option value="weeks">Weeks</option>
                        <option value="months">Months</option>
                      </Select>
                    </FormRow>
                  </div>
                </FormRow>
              </FormSection>

              {/* Milestones Section */}
              {job?.jobType === 'fixed' && (
                <FormSection>
                  <SectionTitle>Milestones (Optional)</SectionTitle>
                  <p style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '1rem' }}>
                    Break down your work into milestones to get paid as you complete each phase.
                  </p>
                  
                  {milestones.map((milestone, index) => (
                    <MilestoneContainer key={index}>
                      <MilestoneHeader>
                        <h5 style={{ margin: 0, fontSize: '0.875rem', fontWeight: '600' }}>
                          Milestone {index + 1}
                        </h5>
                        <Button
                          type="button"
                          variant="danger"
                          size="sm"
                          onClick={() => removeMilestone(index)}
                        >
                          Remove
                        </Button>
                      </MilestoneHeader>
                      
                      <FormRow>
                        <Input
                          placeholder="Milestone description"
                          value={milestone.description}
                          onChange={(e) => updateMilestone(index, 'description', e.target.value)}
                        />
                      </FormRow>
                      
                      <FormRow columns="1fr 1fr">
                        <Input
                          type="number"
                          placeholder="Amount ($)"
                          value={milestone.amount}
                          onChange={(e) => updateMilestone(index, 'amount', e.target.value)}
                        />
                        <Input
                          type="date"
                          placeholder="Due date"
                          value={milestone.dueDate}
                          onChange={(e) => updateMilestone(index, 'dueDate', e.target.value)}
                        />
                      </FormRow>
                    </MilestoneContainer>
                  ))}
                  
                  <AddMilestoneButton
                    type="button"
                    variant="outline"
                    onClick={addMilestone}
                  >
                    + Add Milestone
                  </AddMilestoneButton>
                </FormSection>
              )}

              {/* Questions Section */}
              {job?.questions && job.questions.length > 0 && (
                <FormSection>
                  <SectionTitle>Additional Questions</SectionTitle>
                  {job.questions.map((question, index) => (
                    <div key={index} style={{ marginBottom: '1rem' }}>
                      <TextArea
                        name={`questions.${index}.answer`}
                        label={question.question}
                        placeholder="Your answer..."
                        rows={3}
                        required={question.required}
                      />
                    </div>
                  ))}
                </FormSection>
              )}

              {/* File Attachments */}
              <FormSection>
                <SectionTitle>Attachments (Optional)</SectionTitle>
                <FileUploadArea>
                  <p style={{ margin: 0, color: '#6b7280' }}>
                    Click to upload files or drag and drop
                  </p>
                  <p style={{ margin: '0.5rem 0 0 0', fontSize: '0.75rem', color: '#9ca3af' }}>
                    PDF, DOC, DOCX, TXT up to 10MB each
                  </p>
                </FileUploadArea>
              </FormSection>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                <Button variant="secondary" type="button" onClick={onCancel}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  loading={isSubmitting}
                  disabled={isSubmitting}
                >
                  Submit Proposal
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </Card>
    </FormContainer>
  );
};

export default ProposalForm;
