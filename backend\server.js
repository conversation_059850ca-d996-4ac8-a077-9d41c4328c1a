const app = require('./app');
const connectDB = require('./config/database');

// Pure functions for error handling
const handleUncaughtException = (err) => {
  console.log('UNCAUGHT EXCEPTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  process.exit(1);
};

const handleUnhandledRejection = (server) => (err) => {
  console.log('UNHANDLED REJECTION! 💥 Shutting down...');
  console.log(err.name, err.message);
  server.close(() => {
    process.exit(1);
  });
};

const handleSIGTERM = (server) => () => {
  console.log('👋 SIGTERM RECEIVED. Shutting down gracefully');
  server.close(() => {
    console.log('💥 Process terminated!');
  });
};

// Function to start server
const startServer = (port) => {
  const server = app.listen(port, () => {
    console.log(`App running on port ${port}...`);
  });

  // Set up error handlers
  process.on('uncaughtException', handleUncaughtException);
  process.on('unhandledRejection', handleUnhandledRejection(server));
  process.on('SIGTERM', handleSIGTERM(server));

  return server;
};

// Start the server
const PORT = process.env.PORT || 5000;

// Connect to database and start server
connectDB()
  .then(() => {
    console.log('Database connected successfully');
    startServer(PORT);
  })
  .catch((err) => {
    console.error('Database connection failed:', err);
    process.exit(1);
  });
