import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON>, <PERSON>ton, <PERSON>ge, <PERSON>ert, Tabs } from '../common';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled(Card)`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const ProposalCard = styled(Card)`
  margin-bottom: 1rem;
`;

const ProposalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const JobTitle = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
`;

const ProposalMeta = styled.div`
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
`;

const ProposalAmount = styled.div`
  font-size: 1.125rem;
  font-weight: 600;
  color: #059669;
`;

const ActivityItem = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
  
  &:last-child {
    border-bottom: none;
  }
`;

const ActivityIcon = styled.div`
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: #eff6ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
`;

const ActivityContent = styled.div`
  flex: 1;
`;

const ActivityTitle = styled.div`
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
`;

const ActivityTime = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 2rem;
  color: #6b7280;
`;

const getProposalStatusColor = (status) => {
  switch (status) {
    case 'accepted':
      return 'success';
    case 'pending':
      return 'warning';
    case 'rejected':
      return 'error';
    case 'withdrawn':
      return 'default';
    default:
      return 'default';
  }
};

const FreelancerDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalProposals: 0,
    activeProposals: 0,
    totalEarnings: 0,
    completedJobs: 0
  });
  const [proposals, setProposals] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [proposalsRes, statsRes, activityRes] = await Promise.all([
        axios.get('/api/v1/proposals', { params: { freelancer: user.id } }),
        axios.get('/api/v1/freelancer/stats'),
        axios.get('/api/v1/freelancer/activity')
      ]);

      setProposals(proposalsRes.data.data || []);
      setStats(statsRes.data.data || stats);
      setRecentActivity(activityRes.data.data || []);
    } catch (error) {
      setError('Failed to load dashboard data');
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleWithdrawProposal = async (proposalId) => {
    if (!window.confirm('Are you sure you want to withdraw this proposal?')) {
      return;
    }

    try {
      await axios.put(`/api/v1/proposals/${proposalId}/withdraw`);
      setProposals(proposals.map(proposal => 
        proposal._id === proposalId 
          ? { ...proposal, status: 'withdrawn' } 
          : proposal
      ));
    } catch (error) {
      setError('Failed to withdraw proposal');
    }
  };

  const filterProposals = (proposals, filter) => {
    switch (filter) {
      case 'active':
        return proposals.filter(p => p.status === 'pending');
      case 'accepted':
        return proposals.filter(p => p.status === 'accepted');
      case 'rejected':
        return proposals.filter(p => p.status === 'rejected');
      default:
        return proposals;
    }
  };

  const filteredProposals = filterProposals(proposals, activeTab);

  const tabs = [
    { key: 'active', label: 'Active', count: proposals.filter(p => p.status === 'pending').length },
    { key: 'accepted', label: 'Accepted', count: proposals.filter(p => p.status === 'accepted').length },
    { key: 'rejected', label: 'Rejected', count: proposals.filter(p => p.status === 'rejected').length },
    { key: 'all', label: 'All', count: proposals.length }
  ];

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <DashboardContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', fontWeight: '700', color: '#111827', margin: 0 }}>
          Dashboard
        </h1>
        <Button onClick={() => window.location.href = '/jobs'}>
          Browse Jobs
        </Button>
      </div>

      {error && (
        <Alert variant="error" style={{ marginBottom: '2rem' }}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <StatsGrid>
        <StatCard>
          <StatNumber>{stats.totalProposals}</StatNumber>
          <StatLabel>Total Proposals</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.activeProposals}</StatNumber>
          <StatLabel>Active Proposals</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>${stats.totalEarnings}</StatNumber>
          <StatLabel>Total Earnings</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.completedJobs}</StatNumber>
          <StatLabel>Completed Jobs</StatLabel>
        </StatCard>
      </StatsGrid>

      <ContentGrid>
        {/* Proposals Section */}
        <div>
          <Card noPadding>
            <div style={{ padding: '1.5rem 1.5rem 0 1.5rem' }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#111827', margin: '0 0 1rem 0' }}>
                My Proposals
              </h2>
              <Tabs
                tabs={tabs}
                activeTab={activeTab}
                onChange={setActiveTab}
              />
            </div>

            <div style={{ padding: '1.5rem' }}>
              {filteredProposals.length === 0 ? (
                <EmptyState>
                  <h3 style={{ margin: '0 0 1rem 0', color: '#374151' }}>
                    {activeTab === 'all' ? 'No proposals yet' : `No ${activeTab} proposals`}
                  </h3>
                  <p style={{ margin: '0 0 2rem 0' }}>
                    {activeTab === 'all' 
                      ? 'Start browsing jobs and submit your first proposal.'
                      : `You don't have any ${activeTab} proposals at the moment.`
                    }
                  </p>
                  {activeTab === 'all' && (
                    <Button onClick={() => window.location.href = '/jobs'}>
                      Browse Jobs
                    </Button>
                  )}
                </EmptyState>
              ) : (
                filteredProposals.map(proposal => (
                  <ProposalCard key={proposal._id}>
                    <ProposalHeader>
                      <div>
                        <JobTitle>{proposal.job?.title}</JobTitle>
                        <ProposalMeta>
                          <span>Submitted {new Date(proposal.createdAt).toLocaleDateString()}</span>
                          <span>•</span>
                          <span>{proposal.job?.employer?.firstName} {proposal.job?.employer?.lastName}</span>
                        </ProposalMeta>
                        <ProposalAmount>${proposal.bidAmount}</ProposalAmount>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        <Badge variant={getProposalStatusColor(proposal.status)}>
                          {proposal.status}
                        </Badge>
                        {proposal.status === 'pending' && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleWithdrawProposal(proposal._id)}
                          >
                            Withdraw
                          </Button>
                        )}
                      </div>
                    </ProposalHeader>
                    
                    <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                      {proposal.coverLetter.substring(0, 150)}...
                    </div>
                  </ProposalCard>
                ))
              )}
            </div>
          </Card>
        </div>

        {/* Recent Activity Sidebar */}
        <div>
          <Card title="Recent Activity">
            {recentActivity.length === 0 ? (
              <EmptyState>
                <p style={{ margin: 0 }}>No recent activity</p>
              </EmptyState>
            ) : (
              recentActivity.map((activity, index) => (
                <ActivityItem key={index}>
                  <ActivityIcon>
                    <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </ActivityIcon>
                  <ActivityContent>
                    <ActivityTitle>{activity.title}</ActivityTitle>
                    <ActivityTime>{activity.time}</ActivityTime>
                  </ActivityContent>
                </ActivityItem>
              ))
            )}
          </Card>
        </div>
      </ContentGrid>
    </DashboardContainer>
  );
};

export default FreelancerDashboard;
