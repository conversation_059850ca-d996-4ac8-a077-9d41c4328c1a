{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A comprehensive full-stack web marketplace connecting freelancers with employers", "main": "index.js", "scripts": {"install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "test": "concurrently \"npm run test:backend\" \"npm run test:frontend\"", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test -- --watchAll=false", "test:coverage": "concurrently \"npm run test:coverage:backend\" \"npm run test:coverage:frontend\"", "test:coverage:backend": "cd backend && npm run test:coverage", "test:coverage:frontend": "cd frontend && npm run test:coverage", "lint": "concurrently \"npm run lint:backend\" \"npm run lint:frontend\"", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint", "lint:fix": "concurrently \"npm run lint:fix:backend\" \"npm run lint:fix:frontend\"", "lint:fix:backend": "cd backend && npm run lint:fix", "lint:fix:frontend": "cd frontend && npm run lint:fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,md}\"", "clean": "rimraf node_modules backend/node_modules frontend/node_modules frontend/build backend/dist", "reset": "npm run clean && npm run install-all", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "deploy:production": "chmod +x deploy.sh && ./deploy.sh", "backup": "node scripts/backup.js", "seed": "cd backend && npm run seed", "migrate": "cd backend && npm run migrate"}, "keywords": ["freelance", "marketplace", "jobs", "gigs", "react", "nodejs", "mongodb", "express"], "author": "FreelanceDudes Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/freelancedudes.git"}, "bugs": {"url": "https://github.com/yourusername/freelancedudes/issues"}, "homepage": "https://github.com/yourusername/freelancedudes#readme", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.12.2", "morgan": "^1.10.0"}, "devDependencies": {"concurrently": "^7.6.0", "eslint": "^9.23.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "rimraf": "^5.0.1", "supertest": "^7.1.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["backend", "frontend"]}