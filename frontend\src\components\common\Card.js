import React from 'react';
import styled, { css } from 'styled-components';

const CardContainer = styled.div`
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: box-shadow 0.2s ease-in-out;

  ${props => props.hover && css`
    &:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
  `}

  ${props => props.padding && css`
    padding: ${props.padding};
  `}

  ${props => !props.padding && css`
    padding: 1.5rem;
  `}

  ${props => props.noPadding && css`
    padding: 0;
  `}

  ${props => props.border && css`
    border: 1px solid #e5e7eb;
  `}

  ${props => props.shadow === 'sm' && css`
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  `}

  ${props => props.shadow === 'md' && css`
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  `}

  ${props => props.shadow === 'lg' && css`
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  `}

  ${props => props.shadow === 'none' && css`
    box-shadow: none;
  `}
`;

const CardHeader = styled.div`
  padding: 1.5rem 1.5rem 0 1.5rem;
  border-bottom: ${props => props.divider ? '1px solid #e5e7eb' : 'none'};
  padding-bottom: ${props => props.divider ? '1.5rem' : '0'};
  margin-bottom: ${props => props.divider ? '1.5rem' : '1rem'};
`;

const CardTitle = styled.h3`
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
`;

const CardSubtitle = styled.p`
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
`;

const CardBody = styled.div`
  padding: ${props => props.noPadding ? '0' : '0 1.5rem'};
`;

const CardFooter = styled.div`
  padding: 1.5rem;
  border-top: ${props => props.divider ? '1px solid #e5e7eb' : 'none'};
  background-color: ${props => props.background || 'transparent'};
  display: flex;
  align-items: center;
  justify-content: ${props => props.justify || 'flex-end'};
  gap: 0.75rem;
`;

const Card = ({ 
  children, 
  title, 
  subtitle, 
  header, 
  footer, 
  headerDivider = false,
  footerDivider = false,
  footerBackground,
  footerJustify,
  className,
  ...props 
}) => {
  return (
    <CardContainer className={className} {...props}>
      {(title || subtitle || header) && (
        <CardHeader divider={headerDivider}>
          {title && <CardTitle>{title}</CardTitle>}
          {subtitle && <CardSubtitle>{subtitle}</CardSubtitle>}
          {header}
        </CardHeader>
      )}
      
      <CardBody noPadding={props.noPadding}>
        {children}
      </CardBody>
      
      {footer && (
        <CardFooter 
          divider={footerDivider}
          background={footerBackground}
          justify={footerJustify}
        >
          {footer}
        </CardFooter>
      )}
    </CardContainer>
  );
};

export default Card;
