const express = require('express');
const router = express.Router();
const {
  getProposals,
  getProposal,
  createProposal,
  updateProposal,
  deleteProposal,
  acceptProposal,
  withdrawProposal
} = require('../controllers/proposals');
const { protect, authorize } = require('../middleware/auth');
const advancedResults = require('../middleware/advancedResults');
const Proposal = require('../models/Proposal');

router
  .route('/')
  .get(protect, advancedResults(Proposal, {
    path: 'freelancer job',
    select: 'firstName lastName avatar title'
  }), getProposals)
  .post(protect, authorize('freelancer'), createProposal);

router
  .route('/:id')
  .get(protect, getProposal)
  .put(protect, updateProposal)
  .delete(protect, deleteProposal);

router.route('/:id/accept').put(protect, authorize('employer'), acceptProposal);
router.route('/:id/withdraw').put(protect, withdrawProposal);

module.exports = router;
