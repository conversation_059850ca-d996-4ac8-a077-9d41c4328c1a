import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';

const SelectContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  
  ${props => props.required && css`
    &::after {
      content: ' *';
      color: #dc2626;
    }
  `}
`;

const SelectField = styled.select`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827;
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;

  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  ${props => props.error && css`
    border-color: #dc2626;
    
    &:focus {
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
  `}

  ${props => props.success && css`
    border-color: #059669;
    
    &:focus {
      border-color: #059669;
      box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }
  `}

  ${props => props.size === 'sm' && css`
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    font-size: 0.75rem;
  `}

  ${props => props.size === 'lg' && css`
    padding: 1rem 2.5rem 1rem 1rem;
    font-size: 1rem;
  `}
`;

const HelperText = styled.div`
  font-size: 0.75rem;
  color: #6b7280;

  ${props => props.error && css`
    color: #dc2626;
  `}

  ${props => props.success && css`
    color: #059669;
  `}
`;

const Select = forwardRef(({ 
  label, 
  error, 
  success,
  helperText, 
  required,
  children,
  className,
  size = 'md',
  ...props 
}, ref) => {
  return (
    <SelectContainer className={className}>
      {label && (
        <Label htmlFor={props.id} required={required}>
          {label}
        </Label>
      )}
      
      <SelectField
        ref={ref}
        error={error}
        success={success}
        size={size}
        {...props}
      >
        {children}
      </SelectField>
      
      {(helperText || error) && (
        <HelperText error={!!error} success={success}>
          {error || helperText}
        </HelperText>
      )}
    </SelectContainer>
  );
});

Select.displayName = 'Select';

export default Select;
