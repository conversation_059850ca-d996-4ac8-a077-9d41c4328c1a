import React from 'react';
import styled, { css } from 'styled-components';

const BadgeContainer = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
  border-radius: 9999px;
  white-space: nowrap;

  ${props => props.size === 'sm' && css`
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
  `}

  ${props => props.size === 'lg' && css`
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  `}

  ${props => props.variant === 'default' && css`
    background-color: #f3f4f6;
    color: #374151;
  `}

  ${props => props.variant === 'primary' && css`
    background-color: #eff6ff;
    color: #2563eb;
  `}

  ${props => props.variant === 'success' && css`
    background-color: #f0fdf4;
    color: #059669;
  `}

  ${props => props.variant === 'warning' && css`
    background-color: #fffbeb;
    color: #d97706;
  `}

  ${props => props.variant === 'error' && css`
    background-color: #fef2f2;
    color: #dc2626;
  `}

  ${props => props.variant === 'info' && css`
    background-color: #f0f9ff;
    color: #0284c7;
  `}

  ${props => props.outline && css`
    background-color: transparent;
    border: 1px solid;
    
    ${props.variant === 'default' && css`
      border-color: #d1d5db;
      color: #374151;
    `}
    
    ${props.variant === 'primary' && css`
      border-color: #93c5fd;
      color: #2563eb;
    `}
    
    ${props.variant === 'success' && css`
      border-color: #a7f3d0;
      color: #059669;
    `}
    
    ${props.variant === 'warning' && css`
      border-color: #fde68a;
      color: #d97706;
    `}
    
    ${props.variant === 'error' && css`
      border-color: #fecaca;
      color: #dc2626;
    `}
    
    ${props.variant === 'info' && css`
      border-color: #bae6fd;
      color: #0284c7;
    `}
  `}
`;

const Badge = ({ 
  children, 
  variant = 'default', 
  size = 'md',
  outline = false,
  className,
  ...props 
}) => {
  return (
    <BadgeContainer 
      variant={variant} 
      size={size}
      outline={outline}
      className={className}
      {...props}
    >
      {children}
    </BadgeContainer>
  );
};

export default Badge;
