interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'freelancer' | 'employer';
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Job {
  id: string;
  title: string;
  description: string;
  budget: number;
  status: 'open' | 'in_progress' | 'completed' | 'cancelled';
  skills: string[];
  employerId: string;
  createdAt: Date;
  updatedAt: Date;
}

interface Proposal {
  id: string;
  freelancerId: string;
  jobId: string;
  coverLetter: string;
  bidAmount: number;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

interface Contract {
  id: string;
  jobId: string;
  freelancerId: string;
  employerId: string;
  terms: string;
  status: 'active' | 'completed' | 'terminated';
  createdAt: Date;
  updatedAt: Date;
}
