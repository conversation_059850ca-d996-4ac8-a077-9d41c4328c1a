// frontend/src/components/admin/StatCard.js
import React from 'react';
import { Card, Typography, Icon } from '../common';

const StatCard = ({ title, value, change, icon, isMonetary = false }) => {
  const isPositiveChange = change >= 0;
  
  return (
    <Card className="p-6">
      <div className="flex justify-between">
        <div>
          <Typography variant="body2" className="text-gray-500 mb-1">
            {title}
          </Typography>
          <Typography variant="h2" className="text-2xl font-bold">
            {value}
          </Typography>
          
          <div className={`flex items-center mt-2 ${isPositiveChange ? 'text-green-500' : 'text-red-500'}`}>
            <Icon
              name={isPositiveChange ? 'arrow-up' : 'arrow-down'}
              size={16}
            />
            <Typography variant="body2" className="ml-1">
              {Math.abs(change)}% {isPositiveChange ? 'increase' : 'decrease'}
            </Typography>
          </div>
        </div>
        
        <div className="bg-blue-100 rounded-full p-3">
          <Icon name={icon} className="text-blue-500" size={24} />
        </div>
      </div>
    </Card>
  );
};

export default StatCard;