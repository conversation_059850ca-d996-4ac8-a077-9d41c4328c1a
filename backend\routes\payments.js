const express = require('express');
const router = express.Router();
const {
  getPayments,
  getPayment,
  createPayment,
  refundPayment,
  capturePayment,
  verifyPayment
} = require('../controllers/payments');
const { protect } = require('../middleware/auth');
const advancedResults = require('../middleware/advancedResults');
const Payment = require('../models/Payment');

router
  .route('/')
  .get(protect, advancedResults(Payment, {
    path: 'employer freelancer contract',
    select: 'firstName lastName title'
  }), getPayments)
  .post(protect, createPayment);

router
  .route('/:id')
  .get(protect, getPayment);

router.route('/:id/refund').put(protect, refundPayment);
router.route('/:id/capture').put(protect, capturePayment);
router.route('/:id/verify').get(protect, verifyPayment);

module.exports = router;
