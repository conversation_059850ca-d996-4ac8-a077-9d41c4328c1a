const express = require('express');
const router = express.Router();
const {
  getMessages,
  getMessage,
  createMessage,
  updateMessage,
  deleteMessage,
  markAsRead
} = require('../controllers/messages');
const { protect } = require('../middleware/auth');
const advancedResults = require('../middleware/advancedResults');
const Message = require('../models/Message');

router
  .route('/')
  .get(protect, advancedResults(Message, {
    path: 'sender receiver conversation',
    select: 'firstName lastName'
  }), getMessages)
  .post(protect, createMessage);

router
  .route('/:id')
  .get(protect, getMessage)
  .put(protect, updateMessage)
  .delete(protect, deleteMessage);

router.route('/:id/read').put(protect, markAsRead);

module.exports = router;
