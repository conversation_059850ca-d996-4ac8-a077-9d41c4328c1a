const mongoose = require('mongoose');
const logger = require('../middleware/logger');

const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useCreateIndex: true,
      useFindAndModify: false
    });

    logger.info(`MongoDB Connected: ${conn.connection.host}`.cyan.underline);
  } catch (err) {
    logger.error(`Error: ${err.message}`.red);
    process.exit(1);
  }
};

module.exports = connectDB;
