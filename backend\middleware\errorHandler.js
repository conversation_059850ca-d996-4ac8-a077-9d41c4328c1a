// Error handling middleware
module.exports = function(err, req, res, next) {
  console.error(err.stack);

  // Default to 500 server error if no status code
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';

  res.status(statusCode).json({
    success: false,
    status: statusCode,
    message: message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
};
