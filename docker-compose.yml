version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: freelancedudes-db
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: freelancedudes
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - freelancedudes-network

  # Redis Cache (optional)
  redis:
    image: redis:7-alpine
    container_name: freelancedudes-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - freelancedudes-network

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: freelancedudes-api
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=*************************************************************************
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-production-jwt-secret-here
      - CLIENT_URL=http://localhost:3000
    depends_on:
      - mongodb
      - redis
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    networks:
      - freelancedudes-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web App
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile
    container_name: freelancedudes-web
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - REACT_APP_API_URL=http://localhost:5000/api/v1
    depends_on:
      - backend
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - freelancedudes-network

  # Nginx Load Balancer (for production scaling)
  nginx:
    image: nginx:alpine
    container_name: freelancedudes-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/nginx-lb.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - freelancedudes-network

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  freelancedudes-network:
    driver: bridge
