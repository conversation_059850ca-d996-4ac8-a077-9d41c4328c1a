# Dependencies
node_modules/
/frontend/node_modules/
/backend/node_modules/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
/backend/.env
/frontend/.env

# Build outputs
/frontend/build/
/frontend/dist/
/backend/dist/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/backend/logs/
*.log

# IDE and editor files
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.swp
*.swo
.DS_Store

# Testing
/coverage
/frontend/coverage

# Temp files
/tmp
.tmp/

# Database dumps
*.dump
*.sql
*.sqlite
