import axios from 'axios';

// Pure functions for token management
const getToken = () => localStorage.getItem('token');
const removeToken = () => localStorage.removeItem('token');

// Pure functions for request configuration
const createBaseConfig = () => ({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  }
});

const addAuthHeader = (config) => {
  const token = getToken();
  if (token) {
    return {
      ...config,
      headers: {
        ...config.headers,
        Authorization: `Bearer ${token}`
      }
    };
  }
  return config;
};

// Pure functions for error handling
const isUnauthorizedError = (error) => error.response?.status === 401;

const handleUnauthorizedError = () => {
  removeToken();
  window.location = '/login';
};

// Create API client with functional interceptors
const createApiClient = () => {
  const api = axios.create(createBaseConfig());

  // Request interceptor
  api.interceptors.request.use(
    (config) => addAuthHeader(config),
    (error) => Promise.reject(error)
  );

  // Response interceptor
  api.interceptors.response.use(
    (response) => response,
    (error) => {
      if (isUnauthorizedError(error)) {
        handleUnauthorizedError();
      }
      return Promise.reject(error);
    }
  );

  return api;
};

// Create and export the API client
const api = createApiClient();

export default api;
