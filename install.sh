#!/bin/bash

# FreelanceDudes Installation Script for Linux/macOS
# This script automates the setup process for the FreelanceDudes platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if command_exists apt-get; then
            OS="ubuntu"
        elif command_exists yum; then
            OS="centos"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        OS="unknown"
    fi
    log_info "Detected OS: $OS"
}

# Install Node.js
install_nodejs() {
    log_info "Installing Node.js..."
    
    if command_exists node; then
        NODE_VERSION=$(node --version)
        log_info "Node.js is already installed: $NODE_VERSION"
        
        # Check if version is >= 16
        MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
        if [ "$MAJOR_VERSION" -lt 16 ]; then
            log_warning "Node.js version is less than 16. Updating..."
        else
            log_success "Node.js version is compatible"
            return 0
        fi
    fi
    
    case $OS in
        "ubuntu")
            curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
            sudo apt-get install -y nodejs
            ;;
        "centos")
            curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
            sudo yum install -y nodejs npm
            ;;
        "macos")
            if command_exists brew; then
                brew install node
            else
                log_error "Homebrew not found. Please install Node.js manually from https://nodejs.org/"
                exit 1
            fi
            ;;
        *)
            log_error "Unsupported OS. Please install Node.js manually from https://nodejs.org/"
            exit 1
            ;;
    esac
    
    log_success "Node.js installed successfully"
}

# Install MongoDB
install_mongodb() {
    log_info "Installing MongoDB..."
    
    if command_exists mongod; then
        log_info "MongoDB is already installed"
        return 0
    fi
    
    case $OS in
        "ubuntu")
            wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
            echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
            sudo apt-get update
            sudo apt-get install -y mongodb-org
            sudo systemctl start mongod
            sudo systemctl enable mongod
            ;;
        "centos")
            cat <<EOF | sudo tee /etc/yum.repos.d/mongodb-org-6.0.repo
[mongodb-org-6.0]
name=MongoDB Repository
baseurl=https://repo.mongodb.org/yum/redhat/\$releasever/mongodb-org/6.0/x86_64/
gpgcheck=1
enabled=1
gpgkey=https://www.mongodb.org/static/pgp/server-6.0.asc
EOF
            sudo yum install -y mongodb-org
            sudo systemctl start mongod
            sudo systemctl enable mongod
            ;;
        "macos")
            if command_exists brew; then
                brew tap mongodb/brew
                brew install mongodb-community
                brew services start mongodb-community
            else
                log_error "Homebrew not found. Please install MongoDB manually"
                exit 1
            fi
            ;;
        *)
            log_warning "MongoDB installation not automated for this OS. Please install manually"
            ;;
    esac
    
    log_success "MongoDB installed successfully"
}

# Install Git
install_git() {
    log_info "Checking Git installation..."
    
    if command_exists git; then
        log_success "Git is already installed"
        return 0
    fi
    
    case $OS in
        "ubuntu")
            sudo apt-get update
            sudo apt-get install -y git
            ;;
        "centos")
            sudo yum install -y git
            ;;
        "macos")
            if command_exists brew; then
                brew install git
            else
                log_info "Git should be available via Xcode Command Line Tools"
                xcode-select --install
            fi
            ;;
        *)
            log_error "Please install Git manually"
            exit 1
            ;;
    esac
    
    log_success "Git installed successfully"
}

# Setup project
setup_project() {
    log_info "Setting up project dependencies..."
    
    # Install root dependencies
    if [ -f "package.json" ]; then
        log_info "Installing root dependencies..."
        npm install
    fi
    
    # Install backend dependencies
    if [ -d "backend" ]; then
        log_info "Installing backend dependencies..."
        cd backend
        npm install
        cd ..
    fi
    
    # Install frontend dependencies
    if [ -d "frontend" ]; then
        log_info "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
    fi
    
    log_success "Project dependencies installed successfully"
}

# Setup environment files
setup_environment() {
    log_info "Setting up environment files..."
    
    # Backend environment
    if [ -f "backend/.env.example" ] && [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        log_success "Backend .env file created from example"
        log_warning "Please edit backend/.env with your configuration"
    fi
    
    # Frontend environment
    if [ -f "frontend/.env.example" ] && [ ! -f "frontend/.env" ]; then
        cp frontend/.env.example frontend/.env
        log_success "Frontend .env file created from example"
        log_warning "Please edit frontend/.env with your configuration"
    fi
}

# Create necessary directories
create_directories() {
    log_info "Creating necessary directories..."
    
    mkdir -p backend/uploads
    mkdir -p backend/logs
    mkdir -p frontend/build
    
    log_success "Directories created successfully"
}

# Main installation function
main() {
    echo "=================================================="
    echo "    FreelanceDudes Installation Script"
    echo "=================================================="
    echo ""
    
    # Detect OS
    detect_os
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        log_error "package.json not found. Please run this script from the project root directory."
        exit 1
    fi
    
    # Install dependencies
    install_git
    install_nodejs
    install_mongodb
    
    # Setup project
    setup_project
    setup_environment
    create_directories
    
    echo ""
    echo "=================================================="
    log_success "Installation completed successfully!"
    echo "=================================================="
    echo ""
    echo "Next steps:"
    echo "1. Edit backend/.env with your configuration"
    echo "2. Edit frontend/.env with your configuration (if needed)"
    echo "3. Start the development servers:"
    echo "   - Backend: cd backend && npm run dev"
    echo "   - Frontend: cd frontend && npm start"
    echo ""
    echo "For production deployment, see the README.md file."
    echo ""
}

# Run main function
main "$@"
