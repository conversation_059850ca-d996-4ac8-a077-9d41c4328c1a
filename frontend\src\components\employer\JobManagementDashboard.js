import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON>, <PERSON>ton, Badge, Alert, Tabs } from '../common';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled(Card)`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
`;

const JobsGrid = styled.div`
  display: grid;
  gap: 1.5rem;
`;

const JobCard = styled(Card)`
  transition: transform 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const JobHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const JobTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
`;

const JobMeta = styled.div`
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
`;

const JobDescription = styled.p`
  color: #4b5563;
  margin-bottom: 1rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
`;

const JobFooter = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
`;

const ProposalCount = styled.div`
  font-size: 0.875rem;
  color: #2563eb;
  font-weight: 500;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #6b7280;
`;

const getStatusColor = (status) => {
  switch (status) {
    case 'published':
      return 'success';
    case 'draft':
      return 'warning';
    case 'in-progress':
      return 'info';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

const getStatusLabel = (status) => {
  switch (status) {
    case 'published':
      return 'Active';
    case 'draft':
      return 'Draft';
    case 'in-progress':
      return 'In Progress';
    case 'completed':
      return 'Completed';
    case 'cancelled':
      return 'Cancelled';
    default:
      return status;
  }
};

const JobManagementDashboard = () => {
  const { user } = useAuth();
  const [jobs, setJobs] = useState([]);
  const [stats, setStats] = useState({
    totalJobs: 0,
    activeJobs: 0,
    totalProposals: 0,
    completedJobs: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    fetchJobs();
    fetchStats();
  }, []);

  const fetchJobs = async () => {
    try {
      const response = await axios.get('/api/v1/jobs', {
        params: { employer: user.id }
      });
      setJobs(response.data.data || []);
    } catch (error) {
      setError('Failed to fetch jobs');
      console.error('Error fetching jobs:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/v1/jobs/stats', {
        params: { employer: user.id }
      });
      setStats(response.data.data || stats);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleDeleteJob = async (jobId) => {
    if (!window.confirm('Are you sure you want to delete this job?')) {
      return;
    }

    try {
      await axios.delete(`/api/v1/jobs/${jobId}`);
      setJobs(jobs.filter(job => job._id !== jobId));
    } catch (error) {
      setError('Failed to delete job');
    }
  };

  const handlePublishJob = async (jobId) => {
    try {
      await axios.put(`/api/v1/jobs/${jobId}`, { status: 'published' });
      setJobs(jobs.map(job => 
        job._id === jobId ? { ...job, status: 'published' } : job
      ));
    } catch (error) {
      setError('Failed to publish job');
    }
  };

  const filterJobs = (jobs, filter) => {
    switch (filter) {
      case 'active':
        return jobs.filter(job => job.status === 'published');
      case 'draft':
        return jobs.filter(job => job.status === 'draft');
      case 'completed':
        return jobs.filter(job => job.status === 'completed');
      default:
        return jobs;
    }
  };

  const filteredJobs = filterJobs(jobs, activeTab);

  const tabs = [
    { key: 'all', label: 'All Jobs', count: jobs.length },
    { key: 'active', label: 'Active', count: jobs.filter(j => j.status === 'published').length },
    { key: 'draft', label: 'Drafts', count: jobs.filter(j => j.status === 'draft').length },
    { key: 'completed', label: 'Completed', count: jobs.filter(j => j.status === 'completed').length }
  ];

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <DashboardContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', fontWeight: '700', color: '#111827', margin: 0 }}>
          Job Management
        </h1>
        <Button onClick={() => window.location.href = '/jobs/new'}>
          Post New Job
        </Button>
      </div>

      {error && (
        <Alert variant="error" style={{ marginBottom: '2rem' }}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <StatsGrid>
        <StatCard>
          <StatNumber>{stats.totalJobs}</StatNumber>
          <StatLabel>Total Jobs Posted</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.activeJobs}</StatNumber>
          <StatLabel>Active Jobs</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.totalProposals}</StatNumber>
          <StatLabel>Total Proposals</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>{stats.completedJobs}</StatNumber>
          <StatLabel>Completed Jobs</StatLabel>
        </StatCard>
      </StatsGrid>

      {/* Jobs List */}
      <Card noPadding>
        <div style={{ padding: '1.5rem 1.5rem 0 1.5rem' }}>
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onChange={setActiveTab}
          />
        </div>

        <div style={{ padding: '1.5rem' }}>
          {filteredJobs.length === 0 ? (
            <EmptyState>
              <h3 style={{ margin: '0 0 1rem 0', color: '#374151' }}>
                {activeTab === 'all' ? 'No jobs posted yet' : `No ${activeTab} jobs`}
              </h3>
              <p style={{ margin: '0 0 2rem 0' }}>
                {activeTab === 'all' 
                  ? 'Start by posting your first job to find talented freelancers.'
                  : `You don't have any ${activeTab} jobs at the moment.`
                }
              </p>
              {activeTab === 'all' && (
                <Button onClick={() => window.location.href = '/jobs/new'}>
                  Post Your First Job
                </Button>
              )}
            </EmptyState>
          ) : (
            <JobsGrid>
              {filteredJobs.map(job => (
                <JobCard key={job._id}>
                  <JobHeader>
                    <div>
                      <JobTitle>{job.title}</JobTitle>
                      <Badge variant={getStatusColor(job.status)}>
                        {getStatusLabel(job.status)}
                      </Badge>
                    </div>
                    <ActionButtons>
                      <Button size="sm" variant="outline">
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        Edit
                      </Button>
                      {job.status === 'draft' && (
                        <Button 
                          size="sm" 
                          onClick={() => handlePublishJob(job._id)}
                        >
                          Publish
                        </Button>
                      )}
                      <Button 
                        size="sm" 
                        variant="danger" 
                        onClick={() => handleDeleteJob(job._id)}
                      >
                        Delete
                      </Button>
                    </ActionButtons>
                  </JobHeader>

                  <JobMeta>
                    <span>{job.jobType === 'fixed' ? 'Fixed Price' : 'Hourly'}</span>
                    <span>•</span>
                    <span>
                      {job.jobType === 'fixed' 
                        ? `$${job.budget?.fixed}` 
                        : `$${job.budget?.min}-$${job.budget?.max}/hr`
                      }
                    </span>
                    <span>•</span>
                    <span>{job.category}</span>
                    <span>•</span>
                    <span>Posted {new Date(job.createdAt).toLocaleDateString()}</span>
                  </JobMeta>

                  <JobDescription>{job.description}</JobDescription>

                  <JobFooter>
                    <ProposalCount>
                      {job.proposals?.length || 0} proposals received
                    </ProposalCount>
                    <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                      {job.views || 0} views
                    </div>
                  </JobFooter>
                </JobCard>
              ))}
            </JobsGrid>
          )}
        </div>
      </Card>
    </DashboardContainer>
  );
};

export default JobManagementDashboard;
