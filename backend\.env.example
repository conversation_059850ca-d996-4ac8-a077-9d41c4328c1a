# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
# For local MongoDB
MONGODB_URI=mongodb://localhost:27017/freelancedudes

# For MongoDB Atlas (replace with your connection string)
# MONGODB_URI=mongodb+srv://username:<EMAIL>/freelancedudes?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=30d

# Email Configuration (for notifications and password reset)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=FreelanceDudes

# File Upload Configuration
MAX_FILE_UPLOAD=1000000
FILE_UPLOAD_PATH=./public/uploads

# Payment Gateway Configuration (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=600000

# CORS Configuration
CLIENT_URL=http://localhost:3000

# Session Configuration
SESSION_SECRET=your-session-secret-key-here

# Redis Configuration (optional, for caching)
# REDIS_URL=redis://localhost:6379

# AWS S3 Configuration (optional, for file storage)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_BUCKET_NAME=your-s3-bucket-name
# AWS_REGION=us-east-1

# Google OAuth Configuration (optional)
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth Configuration (optional)
# FACEBOOK_APP_ID=your-facebook-app-id
# FACEBOOK_APP_SECRET=your-facebook-app-secret

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# API Documentation
API_DOCS_ENABLED=true

# Development Configuration
DEBUG=true
VERBOSE_LOGGING=true
