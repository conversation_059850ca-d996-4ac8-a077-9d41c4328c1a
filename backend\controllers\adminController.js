// Admin panel operations controller
const User = require('../models/User');
const Job = require('../models/Job');
const Contract = require('../models/Contract');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get platform statistics
// @route   GET /api/v1/admin/stats
// @access  Private/Admin
exports.getStats = asyncHandler(async (req, res, next) => {
  const stats = await Promise.all([
    User.countDocuments(),
    Job.countDocuments(),
    Contract.countDocuments(),
    User.countDocuments({ role: 'freelancer' }),
    User.countDocuments({ role: 'employer' }),
    Contract.aggregate([
      { $group: { _id: null, totalAmount: { $sum: '$amount' } } }
    ])
  ]);

  res.status(200).json({
    success: true,
    data: {
      totalUsers: stats[0],
      totalJobs: stats[1],
      totalContracts: stats[2],
      totalFreelancers: stats[3],
      totalEmployers: stats[4],
      totalEarnings: stats[5][0]?.totalAmount || 0
    }
  });
});

// @desc    Update user role
// @route   PUT /api/v1/admin/users/:id/role
// @access  Private/Admin
exports.updateUserRole = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return next(
      new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
    );
  }

  // Validate role
  const validRoles = ['user', 'freelancer', 'employer', 'admin'];
  if (!validRoles.includes(req.body.role)) {
    return next(
      new ErrorResponse(`Invalid role specified`, 400)
    );
  }

  user.role = req.body.role;
  await user.save();

  res.status(200).json({ success: true, data: user });
});

// @desc    Toggle user active status
// @route   PUT /api/v1/admin/users/:id/status
// @access  Private/Admin
exports.toggleUserStatus = asyncHandler(async (req, res, next) => {
  const user = await User.findById(req.params.id);

  if (!user) {
    return next(
      new ErrorResponse(`User not found with id of ${req.params.id}`, 404)
    );
  }

  user.isActive = !user.isActive;
  await user.save();

  res.status(200).json({ success: true, data: user });
});

// @desc    Get all flagged content
// @route   GET /api/v1/admin/flagged
// @access  Private/Admin
exports.getFlaggedContent = asyncHandler(async (req, res, next) => {
  const flaggedContent = await Promise.all([
    Job.find({ isFlagged: true }),
    Contract.find({ isFlagged: true }),
    User.find({ isFlagged: true })
  ]);

  res.status(200).json({
    success: true,
    data: {
      flaggedJobs: flaggedContent[0],
      flaggedContracts: flaggedContent[1],
      flaggedUsers: flaggedContent[2]
    }
  });
});
