{"name": "freelancedudes-backend", "version": "1.0.0", "description": "Backend API for FreelanceDudes platform", "main": "server.js", "scripts": {"start": "NODE_ENV=production node server", "dev": "nodemon server", "test": "echo \"Error: no test specified\" && exit 1", "lint": "eslint .", "format": "prettier --write \"**/*.js\""}, "keywords": ["freelance", "node", "express", "mongodb"], "author": "FreelanceDudes Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "colors": "^1.4.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^6.7.0", "helmet": "^6.0.1", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "nodemailer": "^6.9.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "stripe": "^12.0.0", "xss-clean": "^0.1.1"}, "devDependencies": {"eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "nodemon": "^2.0.20", "prettier": "^2.8.3"}}