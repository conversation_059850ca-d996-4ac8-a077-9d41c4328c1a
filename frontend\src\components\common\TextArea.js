import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';

const TextAreaContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  
  ${props => props.required && css`
    &::after {
      content: ' *';
      color: #dc2626;
    }
  `}
`;

const TextAreaField = styled.textarea`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #111827;
  background-color: white;
  transition: all 0.2s ease-in-out;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;

  &::placeholder {
    color: #9ca3af;
  }

  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
    resize: none;
  }

  ${props => props.error && css`
    border-color: #dc2626;
    
    &:focus {
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
  `}

  ${props => props.success && css`
    border-color: #059669;
    
    &:focus {
      border-color: #059669;
      box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }
  `}

  ${props => props.size === 'sm' && css`
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    min-height: 80px;
  `}

  ${props => props.size === 'lg' && css`
    padding: 1rem;
    font-size: 1rem;
    min-height: 120px;
  `}

  ${props => props.rows && css`
    min-height: ${props.rows * 1.5}rem;
  `}
`;

const HelperText = styled.div`
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  justify-content: space-between;
  align-items: center;

  ${props => props.error && css`
    color: #dc2626;
  `}

  ${props => props.success && css`
    color: #059669;
  `}
`;

const CharacterCount = styled.span`
  font-size: 0.75rem;
  color: #9ca3af;
  
  ${props => props.isOverLimit && css`
    color: #dc2626;
  `}
`;

const TextArea = forwardRef(({ 
  label, 
  error, 
  success,
  helperText, 
  required,
  maxLength,
  showCharacterCount = false,
  className,
  size = 'md',
  value = '',
  ...props 
}, ref) => {
  const characterCount = value ? value.length : 0;
  const isOverLimit = maxLength && characterCount > maxLength;

  return (
    <TextAreaContainer className={className}>
      {label && (
        <Label htmlFor={props.id} required={required}>
          {label}
        </Label>
      )}
      
      <TextAreaField
        ref={ref}
        error={error}
        success={success}
        size={size}
        value={value}
        maxLength={maxLength}
        {...props}
      />
      
      {((helperText || error) || (showCharacterCount && maxLength)) && (
        <HelperText error={!!error} success={success}>
          <span>{error || helperText}</span>
          {showCharacterCount && maxLength && (
            <CharacterCount isOverLimit={isOverLimit}>
              {characterCount}/{maxLength}
            </CharacterCount>
          )}
        </HelperText>
      )}
    </TextAreaContainer>
  );
});

TextArea.displayName = 'TextArea';

export default TextArea;
