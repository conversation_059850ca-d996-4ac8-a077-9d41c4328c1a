const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ContractSchema = new Schema({
  proposal: {
    type: Schema.Types.ObjectId,
    ref: 'Proposal',
    required: true
  },
  job: {
    type: Schema.Types.ObjectId,
    ref: 'Job',
    required: true
  },
  freelancer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  employer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: [true, 'Please add a contract title']
  },
  description: {
    type: String,
    required: [true, 'Please add a description']
  },
  terms: {
    type: String,
    required: [true, 'Please add contract terms']
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: Date,
  amount: {
    type: Number,
    required: [true, 'Please add contract amount']
  },
  paymentType: {
    type: String,
    enum: ['fixed', 'hourly'],
    required: [true, 'Please specify payment type']
  },
  status: {
    type: String,
    enum: ['active', 'completed', 'terminated', 'disputed'],
    default: 'active'
  },
  milestones: [{
    description: String,
    amount: Number,
    dueDate: Date,
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'paid'],
      default: 'pending'
    }
  }],
  files: [{
    name: String,
    url: String
  }],
  isFlagged: {
    type: Boolean,
    default: false
  },
  flaggedReason: String,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Update the updatedAt field on save
ContractSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Contract', ContractSchema);
