// Passport configuration for authentication strategies
const passport = require('passport');
const LocalStrategy = require('passport-local').Strategy;
const User = require('../models/User');

module.exports = function(passport) {
  // Local strategy configuration
  passport.use(new LocalStrategy({
    usernameField: 'email',
    passwordField: 'password'
  }, User.authenticate()));

  passport.serializeUser(User.serializeUser());
  passport.deserializeUser(User.deserializeUser());
};
