# FreelanceDudes Deployment Guide

This guide provides step-by-step instructions for deploying the FreelanceDudes platform in various environments.

## 📋 Quick Reference

### Installation Scripts
- **Linux/macOS**: `./install.sh`
- **Windows**: `install.bat`
- **Production VPS**: `./deploy.sh`

### Key Files Created
- `README.md` - Comprehensive project documentation
- `install.sh` - Linux/macOS installation script
- `install.bat` - Windows installation script
- `deploy.sh` - Production deployment script
- `docker-compose.yml` - Docker containerization
- `backend/.env.example` - Backend environment template
- `frontend/.env.example` - Frontend environment template
- `backend/Dockerfile` - Backend container configuration
- `frontend/Dockerfile` - Frontend container configuration
- `backend/healthcheck.js` - Health monitoring script

## 🚀 Quick Start Options

### Option 1: Automated Installation (Recommended)

**Linux/macOS:**
```bash
chmod +x install.sh
./install.sh
```

**Windows:**
```cmd
install.bat
```

### Option 2: Manual Installation

1. **Install Prerequisites**
   - Node.js 16+ 
   - MongoDB 5+
   - Git

2. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd freelancedudes
   npm run install-all
   ```

3. **Configure Environment**
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   # Edit .env files with your configuration
   ```

4. **Start Development**
   ```bash
   npm run dev
   ```

### Option 3: Docker Deployment

```bash
docker-compose up -d
```

## 🌐 Production Deployment

### VPS Deployment (Ubuntu 20.04+)

1. **Automated Deployment**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

2. **Manual Steps After Deployment**
   - Edit `/var/www/freelancedudes/backend/.env`
   - Update domain name in deployment script
   - Restart services: `pm2 restart freelancedudes-api`

### Environment Configuration

#### Backend (.env)
```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/freelancedudes_prod
JWT_SECRET=your-production-secret
CLIENT_URL=https://yourdomain.com
```

#### Frontend (.env)
```env
REACT_APP_API_URL=https://yourdomain.com/api/v1
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_key
```

## 🔧 Post-Deployment Checklist

### Security
- [ ] Update all default passwords
- [ ] Configure firewall (UFW)
- [ ] Setup SSL certificates
- [ ] Enable rate limiting
- [ ] Configure CORS properly

### Monitoring
- [ ] Setup PM2 monitoring
- [ ] Configure log rotation
- [ ] Setup automated backups
- [ ] Monitor disk space
- [ ] Setup uptime monitoring

### Performance
- [ ] Enable gzip compression
- [ ] Configure CDN for static assets
- [ ] Setup database indexing
- [ ] Enable Redis caching
- [ ] Optimize images

## 🛠 Maintenance Commands

### Application Management
```bash
# Check application status
pm2 status

# View logs
pm2 logs freelancedudes-api

# Restart application
pm2 restart freelancedudes-api

# Update application
cd /var/www/freelancedudes
git pull origin main
npm run build
pm2 restart freelancedudes-api
```

### Database Management
```bash
# Backup database
mongodump --db freelancedudes --out /var/backups/

# Restore database
mongorestore --db freelancedudes /var/backups/freelancedudes/

# Check database status
sudo systemctl status mongod
```

### System Monitoring
```bash
# Check system resources
htop
df -h
free -m

# Check nginx status
sudo systemctl status nginx

# View nginx logs
sudo tail -f /var/log/nginx/access.log
```

## 🐳 Docker Commands

### Development
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild containers
docker-compose build --no-cache
```

### Production
```bash
# Deploy to production
docker-compose -f docker-compose.prod.yml up -d

# Scale backend
docker-compose up -d --scale backend=3

# Update containers
docker-compose pull
docker-compose up -d
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   sudo lsof -i :5000
   sudo kill -9 <PID>
   ```

2. **MongoDB Connection Issues**
   ```bash
   sudo systemctl restart mongod
   sudo journalctl -u mongod
   ```

3. **Permission Issues**
   ```bash
   sudo chown -R $USER:$USER /var/www/freelancedudes
   chmod -R 755 /var/www/freelancedudes
   ```

4. **SSL Certificate Issues**
   ```bash
   sudo certbot renew --dry-run
   sudo nginx -t
   sudo systemctl reload nginx
   ```

### Log Locations
- Application: `pm2 logs`
- Nginx: `/var/log/nginx/`
- MongoDB: `/var/log/mongodb/`
- System: `/var/log/syslog`

## 📞 Support

For deployment issues:
1. Check the troubleshooting section
2. Review application logs
3. Consult the main README.md
4. Create an issue on GitHub

## 🔄 Updates and Maintenance

### Regular Updates
```bash
# Weekly maintenance script
#!/bin/bash
cd /var/www/freelancedudes
git pull origin main
npm run build
pm2 restart freelancedudes-api
sudo certbot renew --quiet
/usr/local/bin/backup-freelancedudes.sh
```

### Security Updates
```bash
# Monthly security updates
sudo apt update && sudo apt upgrade -y
npm audit fix
sudo systemctl restart nginx
pm2 restart freelancedudes-api
```

---

**Note**: Always test deployments in a staging environment before applying to production.
