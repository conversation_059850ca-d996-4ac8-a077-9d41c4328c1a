// frontend/src/components/admin/UserManagement.js
import React, { useState, useEffect } from 'react';
import { adminApi } from '../../api/admin';
import {
  Button,
  TextField,
  Select,
  DataTable,
  Modal,
  Alert,
  Typography,
  LoadingSpinner
} from '../common';

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    user_type: '',
    account_status: ''
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  
  useEffect(() => {
    fetchUsers();
  }, [page, limit, filters]);
  
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await adminApi.getUsers({
        page,
        limit,
        ...filters
      });
      
      setUsers(response.data.users);
      setTotalPages(response.data.totalPages);
    } catch (err) {
      setError('Failed to load users');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({
      ...prev,
      [name]: value
    }));
    setPage(1); // Reset to first page when filter changes
  };
  
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };
  
  const handleUpdateUser = async (updatedUserData) => {
    try {
      await adminApi.updateUser(selectedUser.user_id, updatedUserData);
      setIsModalOpen(false);
      fetchUsers();
    } catch (err) {
      setError('Failed to update user');
      console.error(err);
    }
  };
  
  const handleStatusChange = async (userId, newStatus) => {
    try {
      await adminApi.changeUserStatus(userId, newStatus);
      fetchUsers();
    } catch (err) {
      setError(`Failed to change user status to ${newStatus}`);
      console.error(err);
    }
  };
  
  const columns = [
    { header: 'ID', accessor: 'user_id' },
    { 
      header: 'Name', 
      accessor: (row) => `${row.first_name} ${row.last_name}` 
    },
    { header: 'Email', accessor: 'email' },
    { header: 'Type', accessor: 'user_type' },
    { header: 'Status', accessor: 'account_status' },
    { 
      header: 'Joined', 
      accessor: (row) => new Date(row.created_at).toLocaleDateString() 
    },
    {
      header: 'Actions',
      accessor: (row) => (
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="small"
            onClick={() => handleEditUser(row)}
          >
            Edit
          </Button>
          
          {row.account_status === 'active' ? (
            <Button
              variant="danger"
              size="small"
              onClick={() => handleStatusChange(row.user_id, 'suspended')}
            >
              Suspend
            </Button>
          ) : (
            <Button
              variant="success"
              size="small"
              onClick={() => handleStatusChange(row.user_id, 'active')}
            >
              Activate
            </Button>
          )}
        </div>
      )
    }
  ];
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Typography variant="h2" className="text-xl font-semibold">
          User Management
        </Typography>
        
        <Button variant="primary" onClick={() => {/* Add user functionality */}}>
          Add User
        </Button>
      </div>
      
      {error && (
        <Alert variant="error" className="mb-4" onClose={() => setError('')}>
          {error}
        </Alert>
      )}
      
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <TextField
            name="search"
            placeholder="Search by name or email"
            value={filters.search}
            onChange={handleFilterChange}
          />
          
          <Select
            name="user_type"
            value={filters.user_type}
            onChange={handleFilterChange}
            options={[
              { value: '', label: 'All User Types' },
              { value: 'freelancer', label: 'Freelancers' },
              { value: 'employer', label: 'Employers' },
              { value: 'admin', label: 'Administrators' }
            ]}
          />
          
          <Select
            name="account_status"
            value={filters.account_status}
            onChange={handleFilterChange}
            options={[
              { value: '', label: 'All Statuses' },
              { value: 'active', label: 'Active' },
              { value: 'suspended', label: 'Suspended' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'deleted', label: 'Deleted' }
            ]}
          />
        </div>
      </div>
      
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <DataTable
          data={users}
          columns={columns}
          pagination={{
            currentPage: page,
            totalPages,
            onPageChange: setPage,
            limit,
            onLimitChange: setLimit
          }}
        />
      )}
      
      {/* Edit User Modal */}
      {selectedUser && (
        <Modal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          title="Edit User"
        >
          <UserEditForm
            user={selectedUser}
            onSubmit={handleUpdateUser}
            onCancel={() => setIsModalOpen(false)}
          />
        </Modal>
      )}
    </div>
  );
};

const UserEditForm = ({ user, onSubmit, onCancel }) => {
  const [formData, setFormData] = useState({
    first_name: user.first_name,
    last_name: user.last_name,
    email: user.email,
    user_type: user.user_type,
    account_status: user.account_status
  });
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-4">
        <TextField
          label="First Name"
          name="first_name"
          value={formData.first_name}
          onChange={handleChange}
          required
        />
        
        <TextField
          label="Last Name"
          name="last_name"
          value={formData.last_name}
          onChange={handleChange}
          required
        />
        
        <TextField
          label="Email"
          name="email"
          type="email"
          value={formData.email}
          onChange={handleChange}
          required
        />
        
        <Select
          label="User Type"
          name="user_type"
          value={formData.user_type}
          onChange={handleChange}
          options={[
            { value: 'freelancer', label: 'Freelancer' },
            { value: 'employer', label: 'Employer' },
            { value: 'admin', label: 'Administrator' }
          ]}
        />
        
        <Select
          label="Account Status"
          name="account_status"
          value={formData.account_status}
          onChange={handleChange}
          options={[
            { value: 'active', label: 'Active' },
            { value: 'suspended', label: 'Suspended' },
            { value: 'inactive', label: 'Inactive' }
          ]}
        />
        
        <div className="flex justify-end space-x-4 mt-6">
          <Button variant="secondary" onClick={onCancel}>
            Cancel
          </Button>
          <Button variant="primary" type="submit">
            Save Changes
          </Button>
        </div>
      </div>
    </form>
  );
};

export default UserManagement;