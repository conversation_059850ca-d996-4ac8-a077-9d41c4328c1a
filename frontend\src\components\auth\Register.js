// frontend/src/components/auth/Register.js
import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useForm } from '../../hooks/useForm';
import { <PERSON><PERSON>, <PERSON>ton, Card, TextField, Select, Typography } from '../common';

const Register = () => {
  const navigate = useNavigate();
  const { register } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  
  const { values, handleChange } = useForm({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    confirm_password: '',
    user_type: 'freelancer',
    company_name: ''
  });
  
  const userTypes = [
    { value: 'freelancer', label: 'I am a Freelancer' },
    { value: 'employer', label: 'I am an Employer' }
  ];
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    // Validation
    if (values.password !== values.confirm_password) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }
    
    if (values.password.length < 8) {
      setError('Password must be at least 8 characters long');
      setIsLoading(false);
      return;
    }
    
    try {
      const userData = {
        first_name: values.first_name,
        last_name: values.last_name,
        email: values.email,
        password: values.password,
        user_type: values.user_type
      };
      
      if (values.user_type === 'employer' && values.company_name) {
        userData.company_name = values.company_name;
      }
      
      await register(userData);
      navigate('/dashboard');
    } catch (err) {
      setError(err.response?.data?.message || 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 py-8">
      <Card className="w-full max-w-md p-8">
        <Typography variant="h1" className="text-2xl font-bold text-center mb-6">
          Create your FreelanceDudes Account
        </Typography>
        
        {error && (
          <Alert variant="error" className="mb-4">
            {error}
          </Alert>
        )}
        
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <TextField
                label="First Name"
                name="first_name"
                value={values.first_name}
                onChange={handleChange}
                required
              />
              
              <TextField
                label="Last Name"
                name="last_name"
                value={values.last_name}
                onChange={handleChange}
                required
              />
            </div>
            
            <TextField
              label="Email"
              name="email"
              type="email"
              value={values.email}
              onChange={handleChange}
              required
              fullWidth
            />
            
            <TextField
              label="Password"
              name="password"
              type="password"
              value={values.password}
              onChange={handleChange}
              required
              fullWidth
              helperText="Password must be at least 8 characters"
            />
            
            <TextField
              label="Confirm Password"
              name="confirm_password"
              type="password"
              value={values.confirm_password}
              onChange={handleChange}
              required
              fullWidth
            />
            
            <Select
              label="I want to"
              name="user_type"
              value={values.user_type}
              onChange={handleChange}
              options={userTypes}
              required
              fullWidth
            />
            
            {values.user_type === 'employer' && (
              <TextField
                label="Company Name"
                name="company_name"
                value={values.company_name}
                onChange={handleChange}
                fullWidth
              />
            )}
            
            <Button
              type="submit"
              variant="primary"
              fullWidth
              disabled={isLoading}
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </Button>
          </div>
        </form>
        
        <div className="mt-6 text-center">
          <Typography variant="body2">
            Already have an account?{' '}
            <Link to="/login" className="text-blue-600 hover:text-blue-800">
              Log in
            </Link>
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default Register;