import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const AdminDashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
`;

const StatTitle = styled.h3`
  font-size: 1rem;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 0.5rem;
`;

const StatValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
`;

const UsersTable = styled.table`
  width: 100%;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-collapse: collapse;
`;

const TableHeader = styled.thead`
  background-color: #f3f4f6;
`;

const TableRow = styled.tr`
  border-bottom: 1px solid #e5e7eb;
  
  &:last-child {
    border-bottom: none;
  }
`;

const TableHeaderCell = styled.th`
  padding: 1rem;
  text-align: left;
  font-weight: 500;
  color: #4b5563;
`;

const TableCell = styled.td`
  padding: 1rem;
  color: #1f2937;
`;

const UserTypeBadge = styled.span`
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  
  ${props => {
    switch (props.type) {
      case 'admin':
        return 'background-color: #fef3c7; color: #92400e;';
      case 'freelancer':
        return 'background-color: #dbeafe; color: #1e40af;';
      case 'employer':
        return 'background-color: #dcfce7; color: #166534;';
      default:
        return 'background-color: #f3f4f6; color: #4b5563;';
    }
  }}
`;

const ActionButton = styled.button`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  margin-right: 0.5rem;
  
  ${props => {
    switch (props.variant) {
      case 'edit':
        return 'background-color: #f3f4f6; color: #4b5563;';
      case 'delete':
        return 'background-color: #fee2e2; color: #dc2626;';
      default:
        return 'background-color: #f3f4f6; color: #4b5563;';
    }
  }}
  
  &:hover {
    opacity: 0.9;
  }
`;

const AdminDashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalJobs: 0,
    totalApplications: 0,
    activeConversations: 0
  });
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [statsResponse, usersResponse] = await Promise.all([
          axios.get('/api/admin/stats'),
          axios.get('/api/admin/users')
        ]);
        setStats(statsResponse.data);
        setUsers(usersResponse.data);
      } catch (error) {
        console.error('Error fetching admin data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await axios.delete(`/api/admin/users/${userId}`);
        setUsers(users.filter(user => user.id !== userId));
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <AdminDashboardContainer>
      <Title>Admin Dashboard</Title>

      <StatsGrid>
        <StatCard>
          <StatTitle>Total Users</StatTitle>
          <StatValue>{stats.totalUsers}</StatValue>
        </StatCard>
        <StatCard>
          <StatTitle>Total Jobs</StatTitle>
          <StatValue>{stats.totalJobs}</StatValue>
        </StatCard>
        <StatCard>
          <StatTitle>Total Applications</StatTitle>
          <StatValue>{stats.totalApplications}</StatValue>
        </StatCard>
        <StatCard>
          <StatTitle>Active Conversations</StatTitle>
          <StatValue>{stats.activeConversations}</StatValue>
        </StatCard>
      </StatsGrid>

      <UsersTable>
        <TableHeader>
          <TableRow>
            <TableHeaderCell>Name</TableHeaderCell>
            <TableHeaderCell>Email</TableHeaderCell>
            <TableHeaderCell>Type</TableHeaderCell>
            <TableHeaderCell>Actions</TableHeaderCell>
          </TableRow>
        </TableHeader>
        <tbody>
          {users.map(user => (
            <TableRow key={user.id}>
              <TableCell>{user.first_name} {user.last_name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>
                <UserTypeBadge type={user.user_type}>
                  {user.user_type}
                </UserTypeBadge>
              </TableCell>
              <TableCell>
                <ActionButton variant="edit">Edit</ActionButton>
                <ActionButton
                  variant="delete"
                  onClick={() => handleDeleteUser(user.id)}
                >
                  Delete
                </ActionButton>
              </TableCell>
            </TableRow>
          ))}
        </tbody>
      </UsersTable>
    </AdminDashboardContainer>
  );
};

export default AdminDashboard; 