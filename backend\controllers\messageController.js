// Messaging system controller
const Message = require('../models/Message');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all messages in a conversation
// @route   GET /api/v1/messages/:conversationId
// @access  Private
exports.getMessages = asyncHandler(async (req, res, next) => {
  const messages = await Message.find({
    conversation: req.params.conversationId
  }).sort('createdAt');

  // Check if user is part of the conversation
  const firstMessage = messages[0];
  if (firstMessage && 
      firstMessage.sender.toString() !== req.user.id && 
      firstMessage.receiver.toString() !== req.user.id) {
    return next(
      new ErrorResponse(`Not authorized to view these messages`, 401)
    );
  }

  res.status(200).json({
    success: true,
    count: messages.length,
    data: messages
  });
});

// @desc    Create new message
// @route   POST /api/v1/messages
// @access  Private
exports.createMessage = asyncHandler(async (req, res, next) => {
  req.body.sender = req.user.id;

  const message = await Message.create(req.body);

  res.status(201).json({ success: true, data: message });
});

// @desc    Update message
// @route   PUT /api/v1/messages/:id
// @access  Private
exports.updateMessage = asyncHandler(async (req, res, next) => {
  let message = await Message.findById(req.params.id);

  if (!message) {
    return next(
      new ErrorResponse(`Message not found with id of ${req.params.id}`, 404)
    );
  }

  // Make sure message belongs to user
  if (message.sender.toString() !== req.user.id) {
    return next(
      new ErrorResponse(`Not authorized to update this message`, 401)
    );
  }

  message = await Message.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: message });
});

// @desc    Delete message
// @route   DELETE /api/v1/messages/:id
// @access  Private
exports.deleteMessage = asyncHandler(async (req, res, next) => {
  const message = await Message.findById(req.params.id);

  if (!message) {
    return next(
      new ErrorResponse(`Message not found with id of ${req.params.id}`, 404)
    );
  }

  // Make sure message belongs to user
  if (message.sender.toString() !== req.user.id) {
    return next(
      new ErrorResponse(`Not authorized to delete this message`, 401)
    );
  }

  await message.remove();

  res.status(200).json({ success: true, data: {} });
});
