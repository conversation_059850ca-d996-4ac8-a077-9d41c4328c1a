// Payment processing controller
const Payment = require('../models/Payment');
const Contract = require('../models/Contract');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// @desc    Get all payments
// @route   GET /api/v1/payments
// @route   GET /api/v1/contracts/:contractId/payments
// @access  Private
exports.getPayments = asyncHandler(async (req, res, next) => {
  if (req.params.contractId) {
    const payments = await Payment.find({ contract: req.params.contractId });
    return res.status(200).json({
      success: true,
      count: payments.length,
      data: payments
    });
  } else {
    res.status(200).json(res.advancedResults);
  }
});

// @desc    Get single payment
// @route   GET /api/v1/payments/:id
// @access  Private
exports.getPayment = asyncHandler(async (req, res, next) => {
  const payment = await Payment.findById(req.params.id).populate({
    path: 'contract',
    select: 'title amount'
  });

  if (!payment) {
    return next(
      new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({ success: true, data: payment });
});

// @desc    Process payment
// @route   POST /api/v1/contracts/:contractId/payments
// @access  Private
exports.processPayment = asyncHandler(async (req, res, next) => {
  const contract = await Contract.findById(req.params.contractId);

  if (!contract) {
    return next(
      new ErrorResponse(`No contract with the id of ${req.params.contractId}`, 404)
    );
  }

  // Create Stripe payment intent
  const paymentIntent = await stripe.paymentIntents.create({
    amount: req.body.amount * 100, // Convert to cents
    currency: 'usd',
    description: `Payment for contract ${contract._id}`,
    payment_method: req.body.paymentMethodId,
    confirm: true,
    receipt_email: req.body.email
  });

  // Create payment record
  const payment = await Payment.create({
    contract: req.params.contractId,
    employer: req.user.id,
    freelancer: contract.freelancer,
    amount: req.body.amount,
    paymentMethod: 'stripe',
    transactionId: paymentIntent.id,
    status: paymentIntent.status
  });

  res.status(201).json({ success: true, data: payment });
});

// @desc    Update payment
// @route   PUT /api/v1/payments/:id
// @access  Private/Admin
exports.updatePayment = asyncHandler(async (req, res, next) => {
  let payment = await Payment.findById(req.params.id);

  if (!payment) {
    return next(
      new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
    );
  }

  payment = await Payment.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: payment });
});
