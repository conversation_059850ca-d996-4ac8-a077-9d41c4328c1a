// Contract management controller
const Contract = require('../models/Contract');
const Proposal = require('../models/Proposal');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all contracts
// @route   GET /api/v1/contracts
// @access  Private
exports.getContracts = asyncHandler(async (req, res, next) => {
  res.status(200).json(res.advancedResults);
});

// @desc    Get single contract
// @route   GET /api/v1/contracts/:id
// @access  Private
exports.getContract = asyncHandler(async (req, res, next) => {
  const contract = await Contract.findById(req.params.id)
    .populate('proposal')
    .populate('freelancer')
    .populate('employer');

  if (!contract) {
    return next(
      new ErrorResponse(`Contract not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({ success: true, data: contract });
});

// @desc    Create contract
// @route   POST /api/v1/proposals/:proposalId/contracts
// @access  Private
exports.createContract = asyncHandler(async (req, res, next) => {
  req.body.proposal = req.params.proposalId;
  req.body.employer = req.user.id;

  const proposal = await Proposal.findById(req.params.proposalId);

  if (!proposal) {
    return next(
      new ErrorResponse(`No proposal with the id of ${req.params.proposalId}`, 404)
    );
  }

  req.body.freelancer = proposal.freelancer;

  const contract = await Contract.create(req.body);

  // Update proposal status to accepted
  proposal.status = 'accepted';
  await proposal.save();

  res.status(201).json({ success: true, data: contract });
});

// @desc    Update contract
// @route   PUT /api/v1/contracts/:id
// @access  Private
exports.updateContract = asyncHandler(async (req, res, next) => {
  let contract = await Contract.findById(req.params.id);

  if (!contract) {
    return next(
      new ErrorResponse(`Contract not found with id of ${req.params.id}`, 404)
    );
  }

  contract = await Contract.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: contract });
});

// @desc    Delete contract
// @route   DELETE /api/v1/contracts/:id
// @access  Private
exports.deleteContract = asyncHandler(async (req, res, next) => {
  const contract = await Contract.findById(req.params.id);

  if (!contract) {
    return next(
      new ErrorResponse(`Contract not found with id of ${req.params.id}`, 404)
    );
  }

  await contract.remove();

  res.status(200).json({ success: true, data: {} });
});
