const express = require('express');
const router = express.Router();
const {
  getContracts,
  getContract,
  createContract,
  updateContract,
  deleteContract,
  completeContract,
  terminateContract,
  disputeContract
} = require('../controllers/contracts');
const { protect } = require('../middleware/auth');
const advancedResults = require('../middleware/advancedResults');
const Contract = require('../models/Contract');

router
  .route('/')
  .get(protect, advancedResults(Contract, {
    path: 'freelancer employer job',
    select: 'firstName lastName avatar title'
  }), getContracts)
  .post(protect, createContract);

router
  .route('/:id')
  .get(protect, getContract)
  .put(protect, updateContract)
  .delete(protect, deleteContract);

router.route('/:id/complete').put(protect, completeContract);
router.route('/:id/terminate').put(protect, terminateContract);
router.route('/:id/dispute').put(protect, disputeContract);

module.exports = router;
