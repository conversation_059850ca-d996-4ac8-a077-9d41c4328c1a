const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'freelancedudes',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

async function initializeDb() {
  try {
    console.log('Reading schema file...');
    const schemaSQL = fs.readFileSync(
      path.join(__dirname, '../config/schema.sql'),
      'utf8'
    );
    
    console.log('Executing schema...');
    await pool.query(schemaSQL);
    
    console.log('Database initialized successfully!');
  } catch (err) {
    console.error('Error initializing database:', err);
  } finally {
    await pool.end();
  }
}

initializeDb();
