# FreelanceDudes

A full-stack web marketplace for freelancers to find their gigs.

## Project Overview

FreelanceDudes.com is a platform connecting freelancers with employers.

## Setup Instructions

### Prerequisites

- Node.js (v14+)
- PostgreSQL
- npm or yarn

### Development Setup

1. Clone the repository
2. Install dependencies:

   ```
   # Backend
   cd backend
   npm install

   # Frontend
   cd ../frontend
   npm install
   ```

3. Set up environment variables:

   - Copy `.env.example` to `.env` in the backend directory
   - Update values as needed

4. Start development servers:

   ```
   # Backend
   cd backend
   npm run dev

   # Frontend (in another terminal)
   cd frontend
   npm start
   ```

## Docker Setup (Optional)

```
docker-compose up
```

## License

[MIT](LICENSE)
