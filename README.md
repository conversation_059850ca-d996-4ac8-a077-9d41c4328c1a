# FreelanceDudes - Freelance Marketplace Platform

A comprehensive full-stack web marketplace connecting freelancers with employers. Built with Node.js, Express, MongoDB, and React.

## 🚀 Features

### For Freelancers

- Browse and search job opportunities
- Submit detailed proposals with milestones
- Manage contracts and track earnings
- Secure payment processing with escrow protection
- Portfolio and profile management
- Real-time messaging with clients

### For Employers

- Post and manage job listings
- Review and manage proposals
- Create and manage contracts
- Secure payment system with milestone releases
- Project tracking and communication tools
- Freelancer discovery and hiring

### For Administrators

- Platform analytics and statistics
- User management and moderation
- Content management and flagging system
- Payment and transaction oversight
- Dispute resolution tools

## 🛠 Technology Stack

### Backend

- **Runtime**: Node.js (v16+)
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Security**: Helmet, bcryptjs, rate limiting
- **File Upload**: Multer
- **Email**: Nodemailer
- **Payment**: Stripe integration
- **Testing**: Jest, Supertest

### Frontend

- **Framework**: React 18
- **Routing**: React Router v6
- **Styling**: Styled Components
- **Forms**: Formik with Yup validation
- **HTTP Client**: Axios
- **Charts**: Recharts
- **Build Tool**: Create React App

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v16.0.0 or higher)
- **npm** (v8.0.0 or higher) or **yarn**
- **MongoDB** (v5.0 or higher)
- **Git**

### System Requirements

- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: At least 2GB free space
- **OS**: Windows 10+, macOS 10.15+, or Ubuntu 18.04+

## 🚀 Quick Start

### Automated Installation

Run the installation script to set up everything automatically:

```bash
# Make the script executable (Linux/macOS)
chmod +x install.sh
./install.sh

# Or for Windows
install.bat
```

### Manual Installation

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/freelancedudes.git
   cd freelancedudes
   ```

2. **Install dependencies**

   ```bash
   # Install root dependencies
   npm install

   # Install backend dependencies
   cd backend
   npm install

   # Install frontend dependencies
   cd ../frontend
   npm install
   ```

3. **Set up environment variables**

   ```bash
   # Backend environment
   cd backend
   cp .env.example .env
   # Edit .env with your configuration

   # Frontend environment (optional)
   cd ../frontend
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development servers**

   ```bash
   # Start backend (from backend directory)
   npm run dev

   # Start frontend (from frontend directory, in a new terminal)
   npm start
   ```

## 🔧 Environment Configuration

### Backend Environment Variables (.env)

Create a `.env` file in the `backend` directory with the following variables:

```env
# Server Configuration
NODE_ENV=development
PORT=5000

# Database
MONGODB_URI=mongodb://localhost:27017/freelancedudes
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/freelancedudes

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=30d

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_EMAIL=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Upload
MAX_FILE_UPLOAD=1000000
FILE_UPLOAD_PATH=./public/uploads

# Payment Gateway (Stripe)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=600000

# Frontend URL (for CORS)
CLIENT_URL=http://localhost:3000
```

### Frontend Environment Variables (.env)

Create a `.env` file in the `frontend` directory:

```env
# API Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1

# Stripe Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# App Configuration
REACT_APP_NAME=FreelanceDudes
REACT_APP_VERSION=1.0.0
```

## 🗄️ Database Setup

### Local MongoDB Setup

1. **Install MongoDB Community Edition**

   - **Windows**: Download from [MongoDB Download Center](https://www.mongodb.com/try/download/community)
   - **macOS**: `brew install mongodb-community`
   - **Ubuntu**: Follow [MongoDB Ubuntu Installation Guide](https://docs.mongodb.com/manual/tutorial/install-mongodb-on-ubuntu/)

2. **Start MongoDB Service**

   ```bash
   # Windows (as Administrator)
   net start MongoDB

   # macOS
   brew services start mongodb-community

   # Ubuntu
   sudo systemctl start mongod
   sudo systemctl enable mongod
   ```

3. **Verify Installation**
   ```bash
   mongo --version
   # or for newer versions
   mongosh --version
   ```

### MongoDB Atlas (Cloud) Setup

1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Add your IP address to the whitelist
4. Create a database user
5. Get your connection string and update `MONGODB_URI` in `.env`

## 🚀 Development

### Available Scripts

#### Backend Scripts

```bash
cd backend

# Start development server with hot reload
npm run dev

# Start production server
npm start

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Format code
npm run format
```

#### Frontend Scripts

```bash
cd frontend

# Start development server
npm start

# Build for production
npm run build

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Eject (not recommended)
npm run eject
```

### Project Structure

```
freelancedudes/
├── backend/                 # Backend API server
│   ├── config/             # Configuration files
│   ├── controllers/        # Route controllers
│   ├── middleware/         # Custom middleware
│   ├── models/            # Database models
│   ├── routes/            # API routes
│   ├── services/          # Business logic services
│   ├── utils/             # Utility functions
│   ├── uploads/           # File uploads directory
│   └── server.js          # Server entry point
├── frontend/               # React frontend application
│   ├── public/            # Static files
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── context/       # React context providers
│   │   ├── hooks/         # Custom React hooks
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── utils/         # Utility functions
│   └── package.json
├── shared/                 # Shared utilities and types
├── scripts/               # Build and deployment scripts
├── docs/                  # Documentation
└── README.md
```

## 🌐 Production Deployment

### VPS Deployment (Ubuntu 20.04+)

#### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js (using NodeSource repository)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install Nginx
sudo apt install nginx -y

# Install PM2 (Process Manager)
sudo npm install -g pm2

# Install Git
sudo apt install git -y
```

#### 2. Clone and Setup Application

```bash
# Clone repository
cd /var/www
sudo git clone https://github.com/yourusername/freelancedudes.git
sudo chown -R $USER:$USER freelancedudes
cd freelancedudes

# Install dependencies
npm install
cd backend && npm install
cd ../frontend && npm install && npm run build
```

#### 3. Environment Configuration

```bash
# Create production environment file
cd /var/www/freelancedudes/backend
sudo cp .env.example .env

# Edit environment variables for production
sudo nano .env
```

Update the `.env` file with production values:

```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/freelancedudes_prod
JWT_SECRET=your-production-jwt-secret-here
CLIENT_URL=https://yourdomain.com
# ... other production configurations
```

#### 4. Start Services with PM2

```bash
# Start MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Start backend with PM2
cd /var/www/freelancedudes/backend
pm2 start server.js --name "freelancedudes-api"

# Save PM2 configuration
pm2 save
pm2 startup
```

#### 5. Configure Nginx

Create Nginx configuration:

```bash
sudo nano /etc/nginx/sites-available/freelancedudes
```

Add the following configuration:

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Serve React build files
    location / {
        root /var/www/freelancedudes/frontend/build;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to backend
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Handle file uploads
    location /uploads/ {
        root /var/www/freelancedudes/backend/public;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/freelancedudes /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### 6. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal (optional, usually set up automatically)
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Docker Deployment

#### 1. Create Docker Files

**Dockerfile (Backend)**:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5000

CMD ["npm", "start"]
```

**Dockerfile (Frontend)**:

```dockerfile
FROM node:18-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 2. Docker Compose

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:6.0
    container_name: freelancedudes-db
    restart: unless-stopped
    ports:
      - '27017:27017'
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: freelancedudes
    volumes:
      - mongodb_data:/data/db

  backend:
    build: ./backend
    container_name: freelancedudes-api
    restart: unless-stopped
    ports:
      - '5000:5000'
    environment:
      - NODE_ENV=production
      - MONGODB_URI=*************************************************************************
    depends_on:
      - mongodb
    volumes:
      - ./backend/uploads:/app/uploads

  frontend:
    build: ./frontend
    container_name: freelancedudes-web
    restart: unless-stopped
    ports:
      - '80:80'
    depends_on:
      - backend

volumes:
  mongodb_data:
```

#### 3. Deploy with Docker

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🔧 Troubleshooting

### Common Issues

#### MongoDB Connection Issues

```bash
# Check MongoDB status
sudo systemctl status mongod

# Check MongoDB logs
sudo journalctl -u mongod

# Restart MongoDB
sudo systemctl restart mongod
```

#### Port Already in Use

```bash
# Find process using port 5000
sudo lsof -i :5000

# Kill process
sudo kill -9 <PID>
```

#### Permission Issues

```bash
# Fix file permissions
sudo chown -R $USER:$USER /var/www/freelancedudes
chmod -R 755 /var/www/freelancedudes
```

#### PM2 Issues

```bash
# Check PM2 status
pm2 status

# Restart application
pm2 restart freelancedudes-api

# View logs
pm2 logs freelancedudes-api

# Reset PM2
pm2 kill
pm2 start server.js --name "freelancedudes-api"
```

### Performance Optimization

#### Backend Optimization

- Enable gzip compression
- Implement Redis caching
- Use database indexing
- Optimize database queries
- Enable API rate limiting

#### Frontend Optimization

- Code splitting with React.lazy()
- Image optimization
- Bundle analysis with webpack-bundle-analyzer
- Service worker for caching
- CDN for static assets

## 🧪 Testing

### Running Tests

```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test

# Run all tests with coverage
npm run test:coverage
```

### Test Structure

```
tests/
├── backend/
│   ├── unit/           # Unit tests
│   ├── integration/    # Integration tests
│   └── e2e/           # End-to-end tests
└── frontend/
    ├── components/     # Component tests
    ├── pages/         # Page tests
    └── utils/         # Utility tests
```

## 📊 Monitoring & Logging

### Production Monitoring

```bash
# PM2 monitoring
pm2 monit

# System monitoring
htop
df -h
free -m

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Application logs
pm2 logs freelancedudes-api
```

### Log Management

```bash
# Rotate PM2 logs
pm2 install pm2-logrotate

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## 🔐 Security Considerations

### Production Security Checklist

- [ ] Use HTTPS with valid SSL certificates
- [ ] Implement rate limiting
- [ ] Use strong JWT secrets
- [ ] Enable CORS properly
- [ ] Sanitize user inputs
- [ ] Use helmet.js for security headers
- [ ] Keep dependencies updated
- [ ] Use environment variables for secrets
- [ ] Implement proper error handling
- [ ] Enable MongoDB authentication
- [ ] Use firewall (ufw) to restrict ports
- [ ] Regular security updates

### Security Commands

```bash
# Enable firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# Check for security updates
sudo apt list --upgradable
sudo apt upgrade

# Monitor failed login attempts
sudo tail -f /var/log/auth.log
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow ESLint and Prettier configurations
- Write tests for new features
- Update documentation as needed
- Use conventional commit messages
- Ensure all tests pass before submitting PR

## 📝 API Documentation

The API documentation is available at:

- Development: `http://localhost:5000/api-docs`
- Production: `https://yourdomain.com/api-docs`

### Key API Endpoints

```
Authentication:
POST /api/v1/auth/register
POST /api/v1/auth/login
GET  /api/v1/auth/me

Jobs:
GET    /api/v1/jobs
POST   /api/v1/jobs
GET    /api/v1/jobs/:id
PUT    /api/v1/jobs/:id
DELETE /api/v1/jobs/:id

Proposals:
GET  /api/v1/proposals
POST /api/v1/proposals
PUT  /api/v1/proposals/:id

Contracts:
GET  /api/v1/contracts
POST /api/v1/contracts
PUT  /api/v1/contracts/:id

Payments:
GET  /api/v1/payments
POST /api/v1/payments
PUT  /api/v1/payments/:id/release
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Node.js](https://nodejs.org/) - JavaScript runtime
- [React](https://reactjs.org/) - Frontend framework
- [MongoDB](https://www.mongodb.com/) - Database
- [Express.js](https://expressjs.com/) - Backend framework
- [Styled Components](https://styled-components.com/) - CSS-in-JS styling

## 📞 Support

For support, email <EMAIL> or join our Slack channel.

## 🗺️ Roadmap

- [ ] Real-time messaging system
- [ ] Video call integration
- [ ] Mobile app development
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] AI-powered job matching
- [ ] Blockchain-based payments
- [ ] Advanced dispute resolution

---

**Made with ❤️ by the FreelanceDudes Team**
