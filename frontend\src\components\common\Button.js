import React from 'react';
import styled, { css } from 'styled-components';

const ButtonBase = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
  gap: 0.5rem;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  ${props => props.size === 'sm' && css`
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  `}

  ${props => props.size === 'lg' && css`
    padding: 1rem 2rem;
    font-size: 1rem;
  `}

  ${props => props.variant === 'primary' && css`
    background-color: #2563eb;
    color: white;

    &:hover:not(:disabled) {
      background-color: #1d4ed8;
    }
  `}

  ${props => props.variant === 'secondary' && css`
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;

    &:hover:not(:disabled) {
      background-color: #e5e7eb;
    }
  `}

  ${props => props.variant === 'danger' && css`
    background-color: #dc2626;
    color: white;

    &:hover:not(:disabled) {
      background-color: #b91c1c;
    }
  `}

  ${props => props.variant === 'success' && css`
    background-color: #059669;
    color: white;

    &:hover:not(:disabled) {
      background-color: #047857;
    }
  `}

  ${props => props.variant === 'outline' && css`
    background-color: transparent;
    color: #2563eb;
    border: 1px solid #2563eb;

    &:hover:not(:disabled) {
      background-color: #2563eb;
      color: white;
    }
  `}

  ${props => props.fullWidth && css`
    width: 100%;
  `}
`;

const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  fullWidth = false,
  loading = false,
  icon,
  ...props 
}) => {
  return (
    <ButtonBase 
      variant={variant} 
      size={size} 
      fullWidth={fullWidth}
      disabled={loading || props.disabled}
      {...props}
    >
      {loading && <Spinner size="sm" />}
      {!loading && icon && icon}
      {children}
    </ButtonBase>
  );
};

const Spinner = styled.div`
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  ${props => props.size === 'sm' && css`
    width: 0.75rem;
    height: 0.75rem;
  `}
`;

export default Button;
