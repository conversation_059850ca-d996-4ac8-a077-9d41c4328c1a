const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ReviewSchema = new Schema({
  contract: {
    type: Schema.Types.ObjectId,
    ref: 'Contract',
    required: true
  },
  reviewer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  reviewee: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    required: [true, 'Please add a rating between 1 and 5']
  },
  title: {
    type: String,
    required: [true, 'Please add a review title'],
    maxlength: 100
  },
  comment: {
    type: String,
    required: [true, 'Please add a comment'],
    maxlength: 1000
  },
  isAnonymous: {
    type: Boolean,
    default: false
  },
  response: {
    text: String,
    createdAt: Date
  },
  isFlagged: {
    type: Boolean,
    default: false
  },
  flaggedReason: String,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Prevent duplicate reviews for same contract by same reviewer
ReviewSchema.index({ contract: 1, reviewer: 1 }, { unique: true });

// Update the updatedAt field on save
ReviewSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Review', ReviewSchema);
