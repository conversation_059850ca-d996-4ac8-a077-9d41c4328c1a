@echo off
REM FreelanceDudes Installation Script for Windows
REM This script automates the setup process for the FreelanceDudes platform

setlocal enabledelayedexpansion

echo ==================================================
echo     FreelanceDudes Installation Script
echo ==================================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] package.json not found. Please run this script from the project root directory.
    pause
    exit /b 1
)

echo [INFO] Starting installation process...
echo.

REM Check for Node.js
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Node.js not found. Please install Node.js from https://nodejs.org/
    echo [INFO] Download the LTS version ^(18.x or higher^)
    echo [INFO] After installation, restart this script.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [SUCCESS] Node.js is installed: !NODE_VERSION!
)

REM Check for npm
echo [INFO] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm not found. Please reinstall Node.js.
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo [SUCCESS] npm is installed: !NPM_VERSION!
)

REM Check for Git
echo [INFO] Checking Git installation...
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] Git not found. Please install Git from https://git-scm.com/
    echo [INFO] Git is recommended for version control and updates.
) else (
    for /f "tokens=*" %%i in ('git --version') do set GIT_VERSION=%%i
    echo [SUCCESS] Git is installed: !GIT_VERSION!
)

REM Check for MongoDB
echo [INFO] Checking MongoDB installation...
mongod --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARNING] MongoDB not found locally.
    echo [INFO] You can either:
    echo [INFO] 1. Install MongoDB Community Edition from https://www.mongodb.com/try/download/community
    echo [INFO] 2. Use MongoDB Atlas ^(cloud database^) - update MONGODB_URI in .env file
    echo.
) else (
    echo [SUCCESS] MongoDB is installed locally
)

echo.
echo [INFO] Installing project dependencies...
echo.

REM Install root dependencies
if exist "package.json" (
    echo [INFO] Installing root dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install root dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Root dependencies installed
)

REM Install backend dependencies
if exist "backend" (
    echo [INFO] Installing backend dependencies...
    cd backend
    call npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install backend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Backend dependencies installed
)

REM Install frontend dependencies
if exist "frontend" (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    call npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install frontend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Frontend dependencies installed
)

echo.
echo [INFO] Setting up environment files...

REM Setup backend environment
if exist "backend\.env.example" (
    if not exist "backend\.env" (
        copy "backend\.env.example" "backend\.env" >nul
        echo [SUCCESS] Backend .env file created from example
        echo [WARNING] Please edit backend\.env with your configuration
    ) else (
        echo [INFO] Backend .env file already exists
    )
)

REM Setup frontend environment
if exist "frontend\.env.example" (
    if not exist "frontend\.env" (
        copy "frontend\.env.example" "frontend\.env" >nul
        echo [SUCCESS] Frontend .env file created from example
        echo [WARNING] Please edit frontend\.env with your configuration
    ) else (
        echo [INFO] Frontend .env file already exists
    )
)

echo.
echo [INFO] Creating necessary directories...

REM Create directories
if not exist "backend\uploads" mkdir "backend\uploads"
if not exist "backend\logs" mkdir "backend\logs"
if not exist "frontend\build" mkdir "frontend\build"

echo [SUCCESS] Directories created successfully

echo.
echo ==================================================
echo [SUCCESS] Installation completed successfully!
echo ==================================================
echo.
echo Next steps:
echo 1. Edit backend\.env with your configuration
echo 2. Edit frontend\.env with your configuration ^(if needed^)
echo 3. Start MongoDB service ^(if using local MongoDB^):
echo    - Open Services ^(services.msc^) and start MongoDB service
echo    - Or use MongoDB Atlas cloud database
echo 4. Start the development servers:
echo    - Backend: cd backend ^&^& npm run dev
echo    - Frontend: cd frontend ^&^& npm start
echo.
echo For production deployment, see the README.md file.
echo.

REM Create start scripts for convenience
echo [INFO] Creating convenience start scripts...

REM Create start-backend.bat
echo @echo off > start-backend.bat
echo cd backend >> start-backend.bat
echo npm run dev >> start-backend.bat

REM Create start-frontend.bat
echo @echo off > start-frontend.bat
echo cd frontend >> start-frontend.bat
echo npm start >> start-frontend.bat

REM Create start-both.bat
echo @echo off > start-both.bat
echo echo Starting FreelanceDudes Development Servers... >> start-both.bat
echo echo. >> start-both.bat
echo start "Backend Server" cmd /k "cd backend && npm run dev" >> start-both.bat
echo timeout /t 3 /nobreak ^> nul >> start-both.bat
echo start "Frontend Server" cmd /k "cd frontend && npm start" >> start-both.bat
echo echo Both servers are starting in separate windows... >> start-both.bat
echo pause >> start-both.bat

echo [SUCCESS] Created convenience scripts:
echo - start-backend.bat: Start backend server only
echo - start-frontend.bat: Start frontend server only  
echo - start-both.bat: Start both servers in separate windows
echo.

echo Installation complete! Press any key to exit...
pause >nul
