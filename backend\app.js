const express = require('express');
const path = require('path');
const mongoose = require('mongoose');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const xss = require('xss-clean');
const rateLimit = require('express-rate-limit');
const hpp = require('hpp');
const cookieParser = require('cookie-parser');
const mongoSanitize = require('express-mongo-sanitize');
const errorHandler = require('./middleware/errorHandler');
const logger = require('./middleware/logger');

// Load env vars
require('dotenv').config();

// Route files
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const jobRoutes = require('./routes/jobs');
const proposalRoutes = require('./routes/proposals');
const contractRoutes = require('./routes/contracts');
const paymentRoutes = require('./routes/payments');
const reviewRoutes = require('./routes/reviews');
const messageRoutes = require('./routes/messages');
const adminRoutes = require('./routes/admin');

// Pure functions for middleware setup
const setupSecurityMiddleware = (app) => {
  app.use(helmet());
  app.use(xss());
  app.use(hpp());
  app.use(mongoSanitize());
  return app;
};

const setupRateLimiting = (app) => {
  const limiter = rateLimit({
    windowMs: 10 * 60 * 1000,
    max: 100
  });
  app.use(limiter);
  return app;
};

const setupLogging = (app) => {
  if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
  }
  return app;
};

const setupRoutes = (app) => {
  app.use('/api/v1/auth', authRoutes);
  app.use('/api/v1/users', userRoutes);
  app.use('/api/v1/jobs', jobRoutes);
  app.use('/api/v1/proposals', proposalRoutes);
  app.use('/api/v1/contracts', contractRoutes);
  app.use('/api/v1/payments', paymentRoutes);
  app.use('/api/v1/reviews', reviewRoutes);
  app.use('/api/v1/messages', messageRoutes);
  app.use('/api/v1/admin', adminRoutes);
  return app;
};

const connectDatabase = () => {
  return mongoose.connect(process.env.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true
  });
};

// Initialize express app
const createApp = () => {
  const app = express();

  // Basic middleware
  app.use(express.json());
  app.use(cookieParser());
  app.use(cors());
  app.use(express.static(path.join(__dirname, 'public')));

  // Compose middleware
  return [
    setupSecurityMiddleware,
    setupRateLimiting,
    setupLogging,
    setupRoutes
  ].reduce((app, middleware) => middleware(app), app);
};

// Create and configure the app
const app = createApp();

// Error handler middleware
app.use(errorHandler);

// Connect to database
connectDatabase()
  .then(() => console.log('MongoDB Connected...'))
  .catch(err => console.log(err));

module.exports = app;
