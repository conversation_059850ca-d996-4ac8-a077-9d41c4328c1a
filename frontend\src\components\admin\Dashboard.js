// frontend/src/components/admin/Dashboard.js
import React, { useEffect, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { adminApi } from '../../api/admin';
import { 
  Card,
  Typography,
  Button,
  Tabs,
  Tab,
  Alert,
  <PERSON>hart,
  <PERSON>hart,
  <PERSON>hart,
  DataTable,
  LoadingSpinner
} from '../common';
import StatCard from './StatCard';
import UserManagement from './UserManagement';
import RevenueAnalytics from './RevenueAnalytics';
import DisputeManagement from './DisputeManagement';

const AdminDashboard = () => {
  const { currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  useEffect(() => {
    fetchDashboardData();
  }, []);
  
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      const response = await adminApi.getDashboardStats();
      setStats(response.data);
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!currentUser || currentUser.user_type !== 'admin') {
    return (
      <Alert variant="error">
        You do not have permission to access this page.
      </Alert>
    );
  }
  
  if (isLoading) {
    return <LoadingSpinner size="large" />;
  }
  
  if (error) {
    return (
      <Alert variant="error">
        {error}
        <Button onClick={fetchDashboardData} className="ml-4">
          Retry
        </Button>
      </Alert>
    );
  }
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <Typography variant="h1" className="text-3xl font-bold">
          Admin Dashboard
        </Typography>
        
        <div className="flex space-x-4">
          <Button 
            variant="secondary" 
            onClick={fetchDashboardData}
          >
            Refresh Data
          </Button>
          
          <Button 
            variant="primary" 
            onClick={() => {/* Export functionality */}}
          >
            Export Reports
          </Button>
        </div>
      </div>
      
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Users"
          value={stats.userStats.totalUsers}
          change={stats.userStats.growth}
          icon="users"
        />
        
        <StatCard
          title="Active Jobs"
          value={stats.jobStats.activeJobs}
          change={stats.jobStats.growth}
          icon="briefcase"
        />
        
        <StatCard
          title="Monthly Revenue"
          value={`$${stats.revenueStats.monthlyRevenue.toLocaleString()}`}
          change={stats.revenueStats.growth}
          icon="dollar-sign"
          isMonetary
        />
        
        <StatCard
          title="Active Contracts"
          value={stats.contractStats.activeContracts}
          change={stats.contractStats.growth}
          icon="file-text"
        />
      </div>
      
      {/* Tab Navigation */}
      <Tabs
        activeTab={activeTab}
        onChange={setActiveTab}
        className="mb-6"
      >
        <Tab id="overview" label="Overview" />
        <Tab id="users" label="User Management" />
        <Tab id="jobs" label="Job Analytics" />
        <Tab id="revenue" label="Revenue" />
        <Tab id="disputes" label="Disputes" />
        <Tab id="settings" label="Platform Settings" />
      </Tabs>
      
      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-md p-6">
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* User Growth Chart */}
            <Card className="p-4">
              <Typography variant="h2" className="text-xl font-semibold mb-4">
                User Growth
              </Typography>
              <LineChart
                data={stats.userGrowthData}
                xKey="date"
                yKey="count"
                height={300}
              />
            </Card>
            
            {/* Revenue Chart */}
            <Card className="p-4">
              <Typography variant="h2" className="text-xl font-semibold mb-4">
                Revenue Overview
              </Typography>
              <BarChart
                data={stats.revenueData}
                xKey="month"
                yKey="amount"
                height={300}
              />
            </Card>
            
            {/* User Type Distribution */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="p-4">
                <Typography variant="h2" className="text-xl font-semibold mb-4">
                  User Distribution
                </Typography>
                <PieChart
                  data={[
                    { name: 'Freelancers', value: stats.userStats.freelancerCount },
                    { name: 'Employers', value: stats.userStats.employerCount },
                    { name: 'Admins', value: stats.userStats.adminCount }
                  ]}
                  height={250}
                />
              </Card>
              
              <Card className="p-4">
                <Typography variant="h2" className="text-xl font-semibold mb-4">
                  Job Categories
                </Typography>
                <PieChart
                  data={stats.jobCategoryData}
                  height={250}
                />
              </Card>
            </div>
            
            {/* Recent Activity */}
            <Card className="p-4">
              <Typography variant="h2" className="text-xl font-semibold mb-4">
                Recent Activity
              </Typography>
              <DataTable
                data={stats.recentActivity}
                columns={[
                  { header: 'Date', accessor: 'date' },
                  { header: 'User', accessor: 'user' },
                  { header: 'Action', accessor: 'action' },
                  { header: 'Details', accessor: 'details' }
                ]}
              />
            </Card>
          </div>
        )}
        
        {activeTab === 'users' && (
          <UserManagement />
        )}
        
        {activeTab === 'revenue' && (
          <RevenueAnalytics />
        )}
        
        {activeTab === 'disputes' && (
          <DisputeManagement />
        )}
        
        {/* Additional tabs implementation */}
      </div>
    </div>
  );
};

export default AdminDashboard;