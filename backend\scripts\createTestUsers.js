const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
const connectDB = require('../config/database');

async function createTestUsers() {
  try {
    // Connect to database
    await connectDB();

    // Test users data
    const testUsers = [
      {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      },
      {
        email: '<EMAIL>',
        password: 'freelancer123',
        firstName: 'John',
        lastName: 'Freelancer',
        role: 'freelancer'
      },
      {
        email: '<EMAIL>',
        password: 'employer123',
        firstName: 'Jane',
        lastName: 'Employer',
        role: 'employer'
      }
    ];

    // Create each test user
    for (const userData of testUsers) {
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        console.log(`User ${userData.email} already exists. Skipping...`);
        continue;
      }

      // Create new user
      const user = new User({
        email: userData.email,
        password: userData.password, // Password will be hashed by the User model pre-save middleware
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role,
        isVerified: true, // Setting test users as verified
        isActive: true
      });

      await user.save();
      console.log(`Created test user: ${userData.email}`);
    }

    console.log('All test users created successfully!');
  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    process.exit(0);
  }
}

// Run the function
createTestUsers(); 