{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["javascript", "javascriptreact"], "prettier.singleQuote": true, "prettier.semi": true, "prettier.tabWidth": 2, "prettier.trailingComma": "es5", "javascript.updateImportsOnFileMove.enabled": "always", "explorer.confirmDragAndDrop": false, "explorer.confirmDelete": false, "editor.tabSize": 2, "files.exclude": {"**/.git": true, "**/node_modules": true}, "search.exclude": {"**/node_modules": true, "**/build": true}}