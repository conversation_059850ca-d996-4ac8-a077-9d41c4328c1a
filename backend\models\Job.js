const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const JobSchema = new Schema({
  title: {
    type: String,
    required: [true, 'Please add a job title'],
    maxlength: 100
  },
  description: {
    type: String,
    required: [true, 'Please add a description']
  },
  employer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  skillsRequired: [{
    name: String,
    level: {
      type: String,
      enum: ['beginner', 'intermediate', 'expert']
    }
  }],
  jobType: {
    type: String,
    enum: ['fixed', 'hourly'],
    required: true
  },
  budget: {
    min: Number,
    max: Number,
    fixed: Number
  },
  duration: {
    value: Number,
    unit: {
      type: String,
      enum: ['hours', 'days', 'weeks', 'months']
    }
  },
  experienceLevel: {
    type: String,
    enum: ['entry', 'intermediate', 'expert']
  },
  category: {
    type: String,
    required: true
  },
  subcategory: String,
  status: {
    type: String,
    enum: ['draft', 'published', 'in-progress', 'completed', 'cancelled'],
    default: 'draft'
  },
  isRemote: {
    type: Boolean,
    default: false
  },
  location: {
    type: String,
    required: function() { return !this.isRemote; }
  },
  attachments: [{
    name: String,
    url: String
  }],
  questions: [{
    question: String,
    type: {
      type: String,
      enum: ['text', 'multiple-choice', 'file']
    },
    options: [String],
    required: Boolean
  }],
  visibility: {
    type: String,
    enum: ['public', 'private'],
    default: 'public'
  },
  proposals: [{
    type: Schema.Types.ObjectId,
    ref: 'Proposal'
  }],
  isFeatured: {
    type: Boolean,
    default: false
  },
  featuredExpires: Date,
  isUrgent: {
    type: Boolean,
    default: false
  },
  isFlagged: {
    type: Boolean,
    default: false
  },
  flaggedReason: String,
  views: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  expiresAt: Date
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Text index for search functionality
JobSchema.index({
  title: 'text',
  description: 'text',
  'skillsRequired.name': 'text'
});

// Update the updatedAt field on save
JobSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Job', JobSchema);
