const express = require('express');
const router = express.Router();
const {
  getReviews,
  getReview,
  createReview,
  updateReview,
  deleteReview,
  respondToReview
} = require('../controllers/reviews');
const { protect } = require('../middleware/auth');
const advancedResults = require('../middleware/advancedResults');
const Review = require('../models/Review');

router
  .route('/')
  .get(advancedResults(Review, {
    path: 'reviewer reviewee contract',
    select: 'firstName lastName title'
  }), getReviews)
  .post(protect, createReview);

router
  .route('/:id')
  .get(getReview)
  .put(protect, updateReview)
  .delete(protect, deleteReview);

router.route('/:id/respond').put(protect, respondToReview);

module.exports = router;
