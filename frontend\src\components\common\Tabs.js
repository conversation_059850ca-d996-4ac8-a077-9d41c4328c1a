import React from 'react';
import styled, { css } from 'styled-components';

const TabsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const TabList = styled.div`
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  overflow-x: auto;
  scrollbar-width: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
`;

const TabItem = styled.button`
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    color: #111827;
  }
  
  ${props => props.active && css`
    color: #2563eb;
    border-bottom-color: #2563eb;
    font-weight: 600;
  `}
  
  &:focus {
    outline: none;
  }
  
  &:disabled {
    color: #d1d5db;
    cursor: not-allowed;
  }
`;

const TabCount = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.5rem;
  height: 1.5rem;
  padding: 0 0.375rem;
  margin-left: 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  background-color: #f3f4f6;
  border-radius: 9999px;
  
  ${props => props.active && css`
    color: #2563eb;
    background-color: #eff6ff;
  `}
`;

const TabContent = styled.div`
  padding: 1rem 0;
`;

const Tabs = ({ 
  tabs, 
  activeTab, 
  onChange, 
  children,
  variant = 'default',
  className,
  ...props 
}) => {
  const handleTabClick = (key) => {
    if (onChange) {
      onChange(key);
    }
  };

  return (
    <TabsContainer className={className} {...props}>
      <TabList>
        {tabs.map((tab) => (
          <TabItem
            key={tab.key}
            active={activeTab === tab.key}
            onClick={() => handleTabClick(tab.key)}
            disabled={tab.disabled}
          >
            {tab.label}
            {tab.count !== undefined && (
              <TabCount active={activeTab === tab.key}>
                {tab.count}
              </TabCount>
            )}
          </TabItem>
        ))}
      </TabList>
      
      {children && (
        <TabContent>
          {React.Children.toArray(children).find((child, index) => {
            // If child has a key prop that matches activeTab
            if (child.key && child.key === `.$${activeTab}`) {
              return true;
            }
            
            // If no child has a matching key, show the child at the same index as the active tab
            const activeIndex = tabs.findIndex(tab => tab.key === activeTab);
            return index === activeIndex;
          })}
        </TabContent>
      )}
    </TabsContainer>
  );
};

export const TabPanel = ({ children, ...props }) => {
  return <div {...props}>{children}</div>;
};

export default Tabs;
