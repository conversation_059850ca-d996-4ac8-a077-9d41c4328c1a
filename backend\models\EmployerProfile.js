const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const EmployerProfileSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  companyName: String,
  companyWebsite: String,
  companySize: {
    type: String,
    enum: ['1-10', '11-50', '51-200', '201-500', '501-1000', '1000+']
  },
  industry: String,
  companyDescription: String,
  companyLogo: String,
  hiringNeeds: [String],
  paymentVerified: {
    type: Boolean,
    default: false
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    default: 0
  },
  completedJobs: {
    type: Number,
    default: 0
  },
  socialLinks: {
    website: String,
    linkedin: String,
    twitter: String
  },
  billingAddress: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  taxID: String,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('EmployerProfile', EmployerProfileSchema);
