import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON>, <PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON>, Modal } from '../common';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const ContractsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const ContractCard = styled(Card)`
  margin-bottom: 1.5rem;
  transition: transform 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-2px);
  }
`;

const ContractHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const ContractTitle = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
`;

const ContractMeta = styled.div`
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
`;

const ContractAmount = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 1rem;
`;

const ContractDescription = styled.p`
  color: #4b5563;
  margin-bottom: 1rem;
  line-height: 1.6;
`;

const MilestonesList = styled.div`
  margin-top: 1rem;
`;

const MilestoneItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
`;

const MilestoneInfo = styled.div`
  flex: 1;
`;

const MilestoneTitle = styled.div`
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
`;

const MilestoneDetails = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  color: #6b7280;
`;

const getStatusColor = (status) => {
  switch (status) {
    case 'active':
      return 'success';
    case 'completed':
      return 'info';
    case 'terminated':
      return 'error';
    case 'disputed':
      return 'warning';
    default:
      return 'default';
  }
};

const getMilestoneStatusColor = (status) => {
  switch (status) {
    case 'approved':
      return 'success';
    case 'pending':
      return 'warning';
    case 'rejected':
      return 'error';
    case 'paid':
      return 'info';
    default:
      return 'default';
  }
};

const ContractManagement = () => {
  const { user } = useAuth();
  const [contracts, setContracts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('active');
  const [selectedContract, setSelectedContract] = useState(null);
  const [showContractModal, setShowContractModal] = useState(false);

  useEffect(() => {
    fetchContracts();
  }, []);

  const fetchContracts = async () => {
    try {
      const response = await axios.get('/api/v1/contracts');
      setContracts(response.data.data || []);
    } catch (error) {
      setError('Failed to fetch contracts');
      console.error('Error fetching contracts:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteContract = async (contractId) => {
    if (!window.confirm('Are you sure you want to mark this contract as completed?')) {
      return;
    }

    try {
      await axios.put(`/api/v1/contracts/${contractId}/complete`);
      setContracts(contracts.map(contract => 
        contract._id === contractId 
          ? { ...contract, status: 'completed' } 
          : contract
      ));
    } catch (error) {
      setError('Failed to complete contract');
    }
  };

  const handleTerminateContract = async (contractId) => {
    if (!window.confirm('Are you sure you want to terminate this contract?')) {
      return;
    }

    try {
      await axios.put(`/api/v1/contracts/${contractId}/terminate`);
      setContracts(contracts.map(contract => 
        contract._id === contractId 
          ? { ...contract, status: 'terminated' } 
          : contract
      ));
    } catch (error) {
      setError('Failed to terminate contract');
    }
  };

  const handleDisputeContract = async (contractId) => {
    try {
      await axios.put(`/api/v1/contracts/${contractId}/dispute`);
      setContracts(contracts.map(contract => 
        contract._id === contractId 
          ? { ...contract, status: 'disputed' } 
          : contract
      ));
    } catch (error) {
      setError('Failed to create dispute');
    }
  };

  const filterContracts = (contracts, filter) => {
    switch (filter) {
      case 'active':
        return contracts.filter(c => c.status === 'active');
      case 'completed':
        return contracts.filter(c => c.status === 'completed');
      case 'disputed':
        return contracts.filter(c => c.status === 'disputed');
      default:
        return contracts;
    }
  };

  const filteredContracts = filterContracts(contracts, activeTab);

  const tabs = [
    { key: 'active', label: 'Active', count: contracts.filter(c => c.status === 'active').length },
    { key: 'completed', label: 'Completed', count: contracts.filter(c => c.status === 'completed').length },
    { key: 'disputed', label: 'Disputed', count: contracts.filter(c => c.status === 'disputed').length },
    { key: 'all', label: 'All', count: contracts.length }
  ];

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <ContractsContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', fontWeight: '700', color: '#111827', margin: 0 }}>
          Contract Management
        </h1>
      </div>

      {error && (
        <Alert variant="error" style={{ marginBottom: '2rem' }}>
          {error}
        </Alert>
      )}

      <Card noPadding>
        <div style={{ padding: '1.5rem 1.5rem 0 1.5rem' }}>
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onChange={setActiveTab}
          />
        </div>

        <div style={{ padding: '1.5rem' }}>
          {filteredContracts.length === 0 ? (
            <EmptyState>
              <h3 style={{ margin: '0 0 1rem 0', color: '#374151' }}>
                {activeTab === 'all' ? 'No contracts yet' : `No ${activeTab} contracts`}
              </h3>
              <p style={{ margin: 0 }}>
                {activeTab === 'all' 
                  ? 'Contracts will appear here once proposals are accepted.'
                  : `You don't have any ${activeTab} contracts at the moment.`
                }
              </p>
            </EmptyState>
          ) : (
            filteredContracts.map(contract => (
              <ContractCard key={contract._id}>
                <ContractHeader>
                  <div>
                    <ContractTitle>{contract.title}</ContractTitle>
                    <Badge variant={getStatusColor(contract.status)}>
                      {contract.status}
                    </Badge>
                  </div>
                  <ActionButtons>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => {
                        setSelectedContract(contract);
                        setShowContractModal(true);
                      }}
                    >
                      View Details
                    </Button>
                    {contract.status === 'active' && (
                      <>
                        <Button 
                          size="sm" 
                          onClick={() => handleCompleteContract(contract._id)}
                        >
                          Complete
                        </Button>
                        <Button 
                          size="sm" 
                          variant="warning"
                          onClick={() => handleDisputeContract(contract._id)}
                        >
                          Dispute
                        </Button>
                        <Button 
                          size="sm" 
                          variant="danger"
                          onClick={() => handleTerminateContract(contract._id)}
                        >
                          Terminate
                        </Button>
                      </>
                    )}
                  </ActionButtons>
                </ContractHeader>

                <ContractMeta>
                  <span>{contract.paymentType === 'fixed' ? 'Fixed Price' : 'Hourly'}</span>
                  <span>•</span>
                  <span>
                    {user.role === 'freelancer' 
                      ? `Client: ${contract.employer?.firstName} ${contract.employer?.lastName}`
                      : `Freelancer: ${contract.freelancer?.firstName} ${contract.freelancer?.lastName}`
                    }
                  </span>
                  <span>•</span>
                  <span>Started {new Date(contract.startDate).toLocaleDateString()}</span>
                  {contract.endDate && (
                    <>
                      <span>•</span>
                      <span>Due {new Date(contract.endDate).toLocaleDateString()}</span>
                    </>
                  )}
                </ContractMeta>

                <ContractAmount>${contract.amount}</ContractAmount>

                <ContractDescription>{contract.description}</ContractDescription>

                {contract.milestones && contract.milestones.length > 0 && (
                  <MilestonesList>
                    <h4 style={{ fontSize: '1rem', fontWeight: '600', margin: '0 0 0.75rem 0' }}>
                      Milestones
                    </h4>
                    {contract.milestones.map((milestone, index) => (
                      <MilestoneItem key={index}>
                        <MilestoneInfo>
                          <MilestoneTitle>{milestone.description}</MilestoneTitle>
                          <MilestoneDetails>
                            ${milestone.amount} • Due {new Date(milestone.dueDate).toLocaleDateString()}
                          </MilestoneDetails>
                        </MilestoneInfo>
                        <Badge variant={getMilestoneStatusColor(milestone.status)}>
                          {milestone.status}
                        </Badge>
                      </MilestoneItem>
                    ))}
                  </MilestonesList>
                )}
              </ContractCard>
            ))
          )}
        </div>
      </Card>
    </ContractsContainer>
  );
};

export default ContractManagement;
