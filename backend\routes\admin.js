const express = require('express');
const router = express.Router();
const {
  getPlatformStats,
  updateUserRole,
  toggleUserStatus,
  getFlaggedContent,
  resolveFlaggedContent
} = require('../controllers/admin');
const { protect, authorize } = require('../middleware/auth');

router.route('/stats').get(protect, authorize('admin'), getPlatformStats);
router.route('/users/:id/role').put(protect, authorize('admin'), updateUserRole);
router.route('/users/:id/status').put(protect, authorize('admin'), toggleUserStatus);
router.route('/flagged').get(protect, authorize('admin'), getFlaggedContent);
router.route('/flagged/:id/resolve').put(protect, authorize('admin'), resolveFlaggedContent);

module.exports = router;
