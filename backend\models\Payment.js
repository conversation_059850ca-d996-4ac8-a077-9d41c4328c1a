const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const PaymentSchema = new Schema({
  contract: {
    type: Schema.Types.ObjectId,
    ref: 'Contract',
    required: true
  },
  employer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  freelancer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: [true, 'Please add payment amount']
  },
  paymentMethod: {
    type: String,
    enum: ['stripe', 'paypal', 'bank-transfer', 'other'],
    required: [true, 'Please specify payment method']
  },
  transactionId: {
    type: String,
    required: [true, 'Please add transaction ID']
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending'
  },
  fee: {
    type: Number,
    default: 0
  },
  netAmount: {
    type: Number,
    required: [true, 'Please add net amount']
  },
  currency: {
    type: String,
    default: 'USD'
  },
  description: String,
  receiptUrl: String,
  isMilestonePayment: {
    type: Boolean,
    default: false
  },
  milestone: {
    description: String,
    amount: Number
  },
  metadata: Schema.Types.Mixed,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('Payment', PaymentSchema);
