const express = require('express');
const router = express.Router();
const {
  getJobs,
  getJob,
  createJob,
  updateJob,
  deleteJob,
  jobPhotoUpload
} = require('../controllers/jobs');
const { protect, authorize } = require('../middleware/auth');
const advancedResults = require('../middleware/advancedResults');
const Job = require('../models/Job');

router
  .route('/')
  .get(advancedResults(Job, {
    path: 'employer',
    select: 'firstName lastName avatar'
  }), getJobs)
  .post(protect, authorize('employer', 'admin'), createJob);

router
  .route('/:id')
  .get(getJob)
  .put(protect, authorize('employer', 'admin'), updateJob)
  .delete(protect, authorize('employer', 'admin'), deleteJob);

router.route('/:id/photo').put(protect, jobPhotoUpload);

module.exports = router;
