const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const FreelancerProfileSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  skills: [{
    name: String,
    experience: {
      type: String,
      enum: ['beginner', 'intermediate', 'expert']
    }
  }],
  bio: String,
  education: [{
    institution: String,
    degree: String,
    fieldOfStudy: String,
    from: Date,
    to: Date,
    current: Boolean,
    description: String
  }],
  experience: [{
    title: String,
    company: String,
    location: String,
    from: Date,
    to: Date,
    current: Boolean,
    description: String
  }],
  portfolio: [{
    title: String,
    description: String,
    url: String,
    media: String
  }],
  hourlyRate: Number,
  availability: {
    type: String,
    enum: ['full-time', 'part-time', 'as-needed']
  },
  languages: [{
    name: String,
    proficiency: {
      type: String,
      enum: ['basic', 'conversational', 'fluent', 'native']
    }
  }],
  certifications: [{
    name: String,
    issuingOrganization: String,
    issueDate: Date,
    credentialID: String,
    credentialURL: String
  }],
  socialLinks: {
    website: String,
    github: String,
    linkedin: String,
    twitter: String
  },
  rating: {
    type: Number,
    min: 1,
    max: 5,
    default: 0
  },
  completedJobs: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('FreelancerProfile', FreelancerProfileSchema);
