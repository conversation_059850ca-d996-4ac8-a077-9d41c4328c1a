.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding: 2rem;
}

/* Responsive layout */
@media (max-width: 768px) {
  .main-content {
    padding: 1rem;
  }
}

/* Custom utility classes */
.text-primary {
  color: #2563eb;
}

.bg-primary {
  background-color: #2563eb;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}
