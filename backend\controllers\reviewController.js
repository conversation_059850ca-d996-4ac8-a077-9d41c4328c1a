// Reviews and ratings controller
const Review = require('../models/Review');
const Contract = require('../models/Contract');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all reviews
// @route   GET /api/v1/reviews
// @route   GET /api/v1/contracts/:contractId/reviews
// @access  Public
exports.getReviews = asyncHandler(async (req, res, next) => {
  if (req.params.contractId) {
    const reviews = await Review.find({ contract: req.params.contractId });
    return res.status(200).json({
      success: true,
      count: reviews.length,
      data: reviews
    });
  } else {
    res.status(200).json(res.advancedResults);
  }
});

// @desc    Get single review
// @route   GET /api/v1/reviews/:id
// @access  Public
exports.getReview = asyncHandler(async (req, res, next) => {
  const review = await Review.findById(req.params.id).populate({
    path: 'contract',
    select: 'title description'
  });

  if (!review) {
    return next(
      new ErrorResponse(`Review not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({ success: true, data: review });
});

// @desc    Add review
// @route   POST /api/v1/contracts/:contractId/reviews
// @access  Private
exports.addReview = asyncHandler(async (req, res, next) => {
  req.body.contract = req.params.contractId;
  req.body.user = req.user.id;

  const contract = await Contract.findById(req.params.contractId);

  if (!contract) {
    return next(
      new ErrorResponse(`No contract with the id of ${req.params.contractId}`, 404)
    );
  }

  // Check if user is part of the contract
  if (contract.freelancer.toString() !== req.user.id && contract.employer.toString() !== req.user.id) {
    return next(
      new ErrorResponse(`Not authorized to review this contract`, 401)
    );
  }

  // Check if user already reviewed this contract
  const existingReview = await Review.findOne({
    contract: req.params.contractId,
    user: req.user.id
  });

  if (existingReview) {
    return next(
      new ErrorResponse(`You have already reviewed this contract`, 400)
    );
  }

  const review = await Review.create(req.body);

  res.status(201).json({ success: true, data: review });
});

// @desc    Update review
// @route   PUT /api/v1/reviews/:id
// @access  Private
exports.updateReview = asyncHandler(async (req, res, next) => {
  let review = await Review.findById(req.params.id);

  if (!review) {
    return next(
      new ErrorResponse(`Review not found with id of ${req.params.id}`, 404)
    );
  }

  // Make sure review belongs to user or user is admin
  if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse(`Not authorized to update this review`, 401)
    );
  }

  review = await Review.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: review });
});

// @desc    Delete review
// @route   DELETE /api/v1/reviews/:id
// @access  Private
exports.deleteReview = asyncHandler(async (req, res, next) => {
  const review = await Review.findById(req.params.id);

  if (!review) {
    return next(
      new ErrorResponse(`Review not found with id of ${req.params.id}`, 404)
    );
  }

  // Make sure review belongs to user or user is admin
  if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
    return next(
      new ErrorResponse(`Not authorized to delete this review`, 401)
    );
  }

  await review.remove();

  res.status(200).json({ success: true, data: {} });
});
