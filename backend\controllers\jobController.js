// backend/controllers/jobController.js
const { validationResult } = require('express-validator');
const Job = require('../models/Job');
const EmployerProfile = require('../models/EmployerProfile');
const notificationService = require('../services/notificationService');
const analyticsService = require('../services/analyticsService');

// Create a new job posting
exports.createJob = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Ensure the user is an employer
    const employerProfile = await EmployerProfile.findByUserId(req.user.id);
    if (!employerProfile) {
      return res.status(403).json({ message: 'Only employers can post jobs' });
    }

    const jobData = {
      employer_id: employerProfile.profile_id,
      title: req.body.title,
      description: req.body.description,
      budget_min: req.body.budget_min,
      budget_max: req.body.budget_max,
      is_hourly: req.body.is_hourly || true,
      duration: req.body.duration,
      location_type: req.body.location_type,
      location: req.body.location,
      expiry_date: req.body.expiry_date
    };

    const job = await Job.create(jobData);

    // Add required skills if provided
    if (req.body.skills && Array.isArray(req.body.skills)) {
      await Promise.all(req.body.skills.map(skill => {
        return Job.addSkill(job.job_id, {
          skill_id: skill.skill_id,
          minimum_years: skill.minimum_years || 0,
          proficiency_level: skill.proficiency_level || 'intermediate',
          is_required: skill.is_required || true
        });
      }));
    }

    // Update employer's job count
    await EmployerProfile.incrementJobCount(employerProfile.profile_id);

    // Track this event for analytics
    analyticsService.trackEvent(req.user.id, 'job_created', {
      job_id: job.job_id,
      employer_id: employerProfile.profile_id
    });

    // Return the created job with skills
    const jobWithSkills = await Job.findById(job.job_id);
    
    res.status(201).json(jobWithSkills);
  } catch (err) {
    console.error('Create job error:', err);
    next(err);
  }
};

// Get all jobs with filtering options
exports.getJobs = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // Construct filters object from query parameters
    const filters = {};
    
    if (req.query.search) filters.search = req.query.search;
    if (req.query.location_type) filters.location_type = req.query.location_type;
    if (req.query.min_budget) filters.min_budget = parseFloat(req.query.min_budget);
    if (req.query.max_budget) filters.max_budget = parseFloat(req.query.max_budget);
    if (req.query.is_hourly) filters.is_hourly = req.query.is_hourly === 'true';
    if (req.query.status) filters.status = req.query.status;
    
    // Skills filtering
    if (req.query.skills) {
      // Convert comma-separated string to array
      filters.skills = req.query.skills.split(',').map(Number);
    }
    
    const jobs = await Job.getAll(page, limit, filters);
    
    res.json(jobs);
  } catch (err) {
    console.error('Get jobs error:', err);
    next(err);
  }
};

// Get a specific job by ID
exports.getJobById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const job = await Job.findById(id);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }
    
    // Increment view count if user is not the employer
    if (req.user && req.user.id) {
      const employerProfile = await EmployerProfile.findByUserId(req.user.id);
      if (!employerProfile || employerProfile.profile_id !== job.employer_id) {
        await Job.incrementViews(id);
        job.views += 1;
      }
    }
    
    // Track job view for analytics
    analyticsService.trackEvent(req.user ? req.user.id : null, 'job_viewed', {
      job_id: job.job_id
    });
    
    res.json(job);
  } catch (err) {
    console.error('Get job error:', err);
    next(err);
  }
};

// Update a job
exports.updateJob = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const { id } = req.params;
    
    // Get the job
    const job = await Job.findById(id);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }
    
    // Check if user is the employer
    const employerProfile = await EmployerProfile.findByUserId(req.user.id);
    if (!employerProfile || employerProfile.profile_id !== job.employer_id) {
      return res.status(403).json({ message: 'Unauthorized to update this job' });
    }
    
    // Prepare update data
    const updateData = {
      title: req.body.title || job.title,
      description: req.body.description || job.description,
      budget_min: req.body.budget_min !== undefined ? req.body.budget_min : job.budget_min,
      budget_max: req.body.budget_max !== undefined ? req.body.budget_max : job.budget_max,
      is_hourly: req.body.is_hourly !== undefined ? req.body.is_hourly : job.is_hourly,
      duration: req.body.duration || job.duration,
      location_type: req.body.location_type || job.location_type,
      location: req.body.location || job.location,
      status: req.body.status || job.status,
      expiry_date: req.body.expiry_date || job.expiry_date
    };
    
    // Update job
    const updatedJob = await Job.update(id, updateData);
    
    // Update skills if provided
    if (req.body.skills && Array.isArray(req.body.skills)) {
      // Remove existing skills
      await Job.removeAllSkills(id);
      
      // Add new skills
      await Promise.all(req.body.skills.map(skill => {
        return Job.addSkill(id, {
          skill_id: skill.skill_id,
          minimum_years: skill.minimum_years || 0,
          proficiency_level: skill.proficiency_level || 'intermediate',
          is_required: skill.is_required || true
        });
      }));
    }
    
    // Get updated job with skills
    const jobWithSkills = await Job.findById(id);
    
    res.json(jobWithSkills);
  } catch (err) {
    console.error('Update job error:', err);
    next(err);
  }
};

// Delete a job
exports.deleteJob = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Get the job
    const job = await Job.findById(id);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }
    
    // Check if user is the employer
    const employerProfile = await EmployerProfile.findByUserId(req.user.id);
    if (!employerProfile || employerProfile.profile_id !== job.employer_id) {
      return res.status(403).json({ message: 'Unauthorized to delete this job' });
    }
    
    // Check if job has active proposals or contracts
    const hasActiveContracts = await Job.hasActiveContracts(id);
    if (hasActiveContracts) {
      return res.status(400).json({ message: 'Cannot delete job with active contracts' });
    }
    
    // Delete job
    await Job.delete(id);
    
    // Decrement employer's job count
    await EmployerProfile.decrementJobCount(employerProfile.profile_id);
    
    res.json({ message: 'Job deleted successfully' });
  } catch (err) {
    console.error('Delete job error:', err);
    next(err);
  }
};

// Get jobs posted by the authenticated employer
exports.getMyJobs = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    
    // Ensure the user is an employer
    const employerProfile = await EmployerProfile.findByUserId(req.user.id);
    if (!employerProfile) {
      return res.status(403).json({ message: 'Only employers can access this endpoint' });
    }
    
    // Get employer's jobs
    const jobs = await Job.getByEmployerId(employerProfile.profile_id, page, limit);
    
    res.json(jobs);
  } catch (err) {
    console.error('Get employer jobs error:', err);
    next(err);
  }
};

// Change job status
exports.changeJobStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // Validate status
    const validStatuses = ['draft', 'open', 'in_progress', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status value' });
    }
    
    // Get the job
    const job = await Job.findById(id);
    if (!job) {
      return res.status(404).json({ message: 'Job not found' });
    }
    
    // Check if user is the employer
    const employerProfile = await EmployerProfile.findByUserId(req.user.id);
    if (!employerProfile || employerProfile.profile_id !== job.employer_id) {
      return res.status(403).json({ message: 'Unauthorized to update this job' });
    }
    
    // Update job status
    const updatedJob = await Job.updateStatus(id, status);
    
    res.json(updatedJob);
  } catch (err) {
    console.error('Change job status error:', err);
    next(err);
  }
};

// Get job statistics for admin panel
exports.getJobStats = async (req, res, next) => {
  try {
    // Check if user is admin
    if (req.user.user_type !== 'admin') {
      return res.status(403).json({ message: 'Unauthorized access' });
    }
    
    const stats = await Job.getStats();
    
    res.json(stats);
  } catch (err) {
    console.error('Get job stats error:', err);
    next(err);
  }
};