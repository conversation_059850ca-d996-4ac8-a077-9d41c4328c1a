// Proposal management controller
const Proposal = require('../models/Proposal');
const Job = require('../models/Job');
const asyncHandler = require('../middleware/async');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all proposals
// @route   GET /api/v1/proposals
// @route   GET /api/v1/jobs/:jobId/proposals
// @access  Private
exports.getProposals = asyncHandler(async (req, res, next) => {
  if (req.params.jobId) {
    const proposals = await Proposal.find({ job: req.params.jobId });
    return res.status(200).json({
      success: true,
      count: proposals.length,
      data: proposals
    });
  } else {
    res.status(200).json(res.advancedResults);
  }
});

// @desc    Get single proposal
// @route   GET /api/v1/proposals/:id
// @access  Private
exports.getProposal = asyncHandler(async (req, res, next) => {
  const proposal = await Proposal.findById(req.params.id).populate({
    path: 'job',
    select: 'title description'
  });

  if (!proposal) {
    return next(
      new ErrorResponse(`Proposal not found with id of ${req.params.id}`, 404)
    );
  }

  res.status(200).json({ success: true, data: proposal });
});

// @desc    Add proposal
// @route   POST /api/v1/jobs/:jobId/proposals
// @access  Private
exports.addProposal = asyncHandler(async (req, res, next) => {
  req.body.job = req.params.jobId;
  req.body.freelancer = req.user.id;

  const job = await Job.findById(req.params.jobId);

  if (!job) {
    return next(
      new ErrorResponse(`No job with the id of ${req.params.jobId}`, 404)
    );
  }

  const proposal = await Proposal.create(req.body);

  res.status(201).json({ success: true, data: proposal });
});

// @desc    Update proposal
// @route   PUT /api/v1/proposals/:id
// @access  Private
exports.updateProposal = asyncHandler(async (req, res, next) => {
  let proposal = await Proposal.findById(req.params.id);

  if (!proposal) {
    return next(
      new ErrorResponse(`Proposal not found with id of ${req.params.id}`, 404)
    );
  }

  proposal = await Proposal.findByIdAndUpdate(req.params.id, req.body, {
    new: true,
    runValidators: true
  });

  res.status(200).json({ success: true, data: proposal });
});

// @desc    Delete proposal
// @route   DELETE /api/v1/proposals/:id
// @access  Private
exports.deleteProposal = asyncHandler(async (req, res, next) => {
  const proposal = await Proposal.findById(req.params.id);

  if (!proposal) {
    return next(
      new ErrorResponse(`Proposal not found with id of ${req.params.id}`, 404)
    );
  }

  await proposal.remove();

  res.status(200).json({ success: true, data: {} });
});
