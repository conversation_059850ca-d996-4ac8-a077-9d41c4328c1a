import React from 'react';
import styled, { css } from 'styled-components';

const AlertContainer = styled.div`
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;

  ${props => props.variant === 'success' && css`
    background-color: #f0fdf4;
    border-color: #bbf7d0;
    color: #166534;
  `}

  ${props => props.variant === 'error' && css`
    background-color: #fef2f2;
    border-color: #fecaca;
    color: #991b1b;
  `}

  ${props => props.variant === 'warning' && css`
    background-color: #fffbeb;
    border-color: #fed7aa;
    color: #92400e;
  `}

  ${props => props.variant === 'info' && css`
    background-color: #eff6ff;
    border-color: #bfdbfe;
    color: #1e40af;
  `}

  ${props => !props.variant && css`
    background-color: #f9fafb;
    border-color: #e5e7eb;
    color: #374151;
  `}
`;

const IconContainer = styled.div`
  flex-shrink: 0;
  margin-top: 0.125rem;
`;

const Content = styled.div`
  flex: 1;
`;

const Title = styled.div`
  font-weight: 500;
  margin-bottom: 0.25rem;
`;

const Message = styled.div`
  font-size: 0.875rem;
  line-height: 1.5;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: currentColor;
  cursor: pointer;
  padding: 0;
  margin-left: auto;
  flex-shrink: 0;
  opacity: 0.7;
  
  &:hover {
    opacity: 1;
  }
`;

// Icon components
const SuccessIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
  </svg>
);

const ErrorIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
  </svg>
);

const WarningIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
  </svg>
);

const InfoIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
  </svg>
);

const CloseIcon = () => (
  <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
  </svg>
);

const getIcon = (variant) => {
  switch (variant) {
    case 'success':
      return <SuccessIcon />;
    case 'error':
      return <ErrorIcon />;
    case 'warning':
      return <WarningIcon />;
    case 'info':
      return <InfoIcon />;
    default:
      return null;
  }
};

const Alert = ({ 
  variant = 'info', 
  title, 
  children, 
  onClose, 
  showIcon = true,
  className,
  ...props 
}) => {
  return (
    <AlertContainer variant={variant} className={className} {...props}>
      {showIcon && (
        <IconContainer>
          {getIcon(variant)}
        </IconContainer>
      )}
      
      <Content>
        {title && <Title>{title}</Title>}
        <Message>{children}</Message>
      </Content>
      
      {onClose && (
        <CloseButton onClick={onClose}>
          <CloseIcon />
        </CloseButton>
      )}
    </AlertContainer>
  );
};

export default Alert;
