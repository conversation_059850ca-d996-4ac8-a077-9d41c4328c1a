import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

const DashboardContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Header = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
`;

const UserName = styled.span`
  font-size: 1.125rem;
  color: #4b5563;
`;

const LogoutButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    background-color: #dc2626;
  }
`;

const Content = styled.div`
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
`;

const AdminDashboard = () => (
  <Content>
    <h2>Admin Dashboard</h2>
    <p>Welcome to the admin dashboard. Here you can manage users, jobs, and system settings.</p>
    {/* Add admin-specific features here */}
  </Content>
);

const FreelancerDashboard = () => (
  <Content>
    <h2>Freelancer Dashboard</h2>
    <p>Welcome to your freelancer dashboard. Here you can find and apply for jobs.</p>
    {/* Add freelancer-specific features here */}
  </Content>
);

const EmployerDashboard = () => (
  <Content>
    <h2>Employer Dashboard</h2>
    <p>Welcome to your employer dashboard. Here you can post and manage jobs.</p>
    {/* Add employer-specific features here */}
  </Content>
);

const Dashboard = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      navigate('/login');
    } else {
      setLoading(false);
    }
  }, [user, navigate]);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  const renderDashboard = () => {
    if (!user) return null;

    switch (user.user_type) {
      case 'admin':
        return <AdminDashboard />;
      case 'freelancer':
        return <FreelancerDashboard />;
      case 'employer':
        return <EmployerDashboard />;
      default:
        return <div>Invalid user type</div>;
    }
  };

  return (
    <DashboardContainer>
      <Header>
        <Title>Dashboard</Title>
        <UserInfo>
          <UserName>
            {user.first_name} {user.last_name}
          </UserName>
          <LogoutButton onClick={handleLogout}>Logout</LogoutButton>
        </UserInfo>
      </Header>
      {renderDashboard()}
    </DashboardContainer>
  );
};

export default Dashboard; 