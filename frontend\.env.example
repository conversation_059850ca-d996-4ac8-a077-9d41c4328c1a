# API Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1

# Stripe Configuration (for payments)
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# App Configuration
REACT_APP_NAME=FreelanceDudes
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=A comprehensive freelance marketplace platform

# Environment
REACT_APP_ENV=development

# Google Analytics (optional)
# REACT_APP_GA_TRACKING_ID=UA-XXXXXXXXX-X

# Google Maps API (optional, for location features)
# REACT_APP_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Social Media Links (optional)
# REACT_APP_FACEBOOK_URL=https://facebook.com/freelancedudes
# REACT_APP_TWITTER_URL=https://twitter.com/freelancedudes
# REACT_APP_LINKEDIN_URL=https://linkedin.com/company/freelancedudes

# Support Configuration
REACT_APP_SUPPORT_EMAIL=<EMAIL>
REACT_APP_CONTACT_EMAIL=<EMAIL>

# Feature Flags (optional)
REACT_APP_ENABLE_CHAT=true
REACT_APP_ENABLE_VIDEO_CALLS=false
REACT_APP_ENABLE_NOTIFICATIONS=true

# Development Configuration
GENERATE_SOURCEMAP=true
REACT_APP_DEBUG=true
