const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const NotificationSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  sender: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  message: {
    type: String,
    required: [true, 'Please add notification message']
  },
  isRead: {
    type: Boolean,
    default: false
  },
  notificationType: {
    type: String,
    enum: [
      'message',
      'proposal',
      'contract',
      'payment',
      'review',
      'system',
      'job',
      'admin'
    ],
    required: true
  },
  relatedEntity: {
    type: Schema.Types.ObjectId,
    refPath: 'relatedEntityModel'
  },
  relatedEntityModel: {
    type: String,
    enum: [
      'User',
      'Message',
      'Proposal',
      'Contract',
      'Payment',
      'Review',
      'Job',
      'System'
    ]
  },
  actionUrl: String,
  metadata: Schema.Types.Mixed,
  createdAt: {
    type: Date,
    default: Date.now
  }
}, {
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Index for faster querying of user notifications
NotificationSchema.index({ user: 1, isRead: 1, createdAt: -1 });

module.exports = mongoose.model('Notification', NotificationSchema);
