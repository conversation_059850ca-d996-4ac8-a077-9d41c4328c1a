import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const MessagesContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  height: calc(100vh - 4rem);
`;

const ConversationsList = styled.div`
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
`;

const ConversationItem = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f3f4f6;
  }
  
  ${props => props.active && `
    background-color: #eff6ff;
    border-left: 4px solid #2563eb;
  `}
`;

const ConversationName = styled.div`
  font-weight: 500;
  color: #1f2937;
`;

const LastMessage = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
`;

const ChatContainer = styled.div`
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ChatHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  font-weight: 500;
  color: #1f2937;
`;

const MessagesList = styled.div`
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Message = styled.div`
  max-width: 70%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  
  ${props => props.sent ? `
    align-self: flex-end;
    background-color: #2563eb;
    color: white;
  ` : `
    align-self: flex-start;
    background-color: #f3f4f6;
    color: #1f2937;
  `}
`;

const MessageInput = styled.div`
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 1rem;
`;

const Input = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

const SendButton = styled.button`
  padding: 0.75rem 1.5rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    background-color: #1d4ed8;
  }
  
  &:disabled {
    background-color: #93c5fd;
    cursor: not-allowed;
  }
`;

const Messages = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchConversations = async () => {
      try {
        const response = await axios.get('/api/conversations');
        setConversations(response.data);
        if (response.data.length > 0) {
          setSelectedConversation(response.data[0].id);
        }
      } catch (error) {
        console.error('Error fetching conversations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchConversations();
  }, []);

  useEffect(() => {
    if (selectedConversation) {
      const fetchMessages = async () => {
        try {
          const response = await axios.get(`/api/conversations/${selectedConversation}/messages`);
          setMessages(response.data);
        } catch (error) {
          console.error('Error fetching messages:', error);
        }
      };

      fetchMessages();
    }
  }, [selectedConversation]);

  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    try {
      await axios.post(`/api/conversations/${selectedConversation}/messages`, {
        content: newMessage
      });
      setNewMessage('');
      // Refresh messages
      const response = await axios.get(`/api/conversations/${selectedConversation}/messages`);
      setMessages(response.data);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <MessagesContainer>
      <ConversationsList>
        {conversations.map(conversation => (
          <ConversationItem
            key={conversation.id}
            active={selectedConversation === conversation.id}
            onClick={() => setSelectedConversation(conversation.id)}
          >
            <ConversationName>{conversation.participant_name}</ConversationName>
            <LastMessage>{conversation.last_message}</LastMessage>
          </ConversationItem>
        ))}
      </ConversationsList>

      {selectedConversation ? (
        <ChatContainer>
          <ChatHeader>
            {conversations.find(c => c.id === selectedConversation)?.participant_name}
          </ChatHeader>
          <MessagesList>
            {messages.map(message => (
              <Message
                key={message.id}
                sent={message.sender_id === user.id}
              >
                {message.content}
              </Message>
            ))}
          </MessagesList>
          <MessageInput as="form" onSubmit={handleSendMessage}>
            <Input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type a message..."
            />
            <SendButton type="submit">Send</SendButton>
          </MessageInput>
        </ChatContainer>
      ) : (
        <ChatContainer>
          <div style={{ padding: '2rem', textAlign: 'center', color: '#6b7280' }}>
            Select a conversation to start messaging
          </div>
        </ChatContainer>
      )}
    </MessagesContainer>
  );
};

export default Messages; 