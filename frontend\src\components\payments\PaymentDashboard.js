import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON>, <PERSON><PERSON>, <PERSON>ge, <PERSON><PERSON>, <PERSON><PERSON>, Modal, Input } from '../common';
import { useAuth } from '../../context/AuthContext';
import axios from 'axios';

const PaymentContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
`;

const StatCard = styled(Card)`
  text-align: center;
`;

const StatNumber = styled.div`
  font-size: 2rem;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 0.5rem;
`;

const StatLabel = styled.div`
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
`;

const PaymentCard = styled(Card)`
  margin-bottom: 1rem;
`;

const PaymentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const PaymentAmount = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 0.5rem;
`;

const PaymentMeta = styled.div`
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
`;

const PaymentDescription = styled.div`
  color: #4b5563;
  margin-bottom: 1rem;
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
`;

const EscrowSection = styled.div`
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
`;

const EscrowTitle = styled.h4`
  font-size: 1rem;
  font-weight: 600;
  color: #166534;
  margin: 0 0 0.5rem 0;
`;

const EscrowAmount = styled.div`
  font-size: 1.25rem;
  font-weight: 700;
  color: #059669;
`;

const WithdrawalForm = styled.div`
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
`;

const getPaymentStatusColor = (status) => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'pending':
      return 'warning';
    case 'failed':
      return 'error';
    case 'refunded':
      return 'info';
    default:
      return 'default';
  }
};

const PaymentDashboard = () => {
  const { user } = useAuth();
  const [payments, setPayments] = useState([]);
  const [stats, setStats] = useState({
    totalEarnings: 0,
    pendingPayments: 0,
    escrowBalance: 0,
    availableBalance: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawing, setWithdrawing] = useState(false);

  useEffect(() => {
    fetchPayments();
    fetchPaymentStats();
  }, []);

  const fetchPayments = async () => {
    try {
      const response = await axios.get('/api/v1/payments');
      setPayments(response.data.data || []);
    } catch (error) {
      setError('Failed to fetch payments');
      console.error('Error fetching payments:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchPaymentStats = async () => {
    try {
      const response = await axios.get('/api/v1/payments/stats');
      setStats(response.data.data || stats);
    } catch (error) {
      console.error('Error fetching payment stats:', error);
    }
  };

  const handleReleaseEscrow = async (paymentId) => {
    if (!window.confirm('Are you sure you want to release this escrow payment?')) {
      return;
    }

    try {
      await axios.put(`/api/v1/payments/${paymentId}/release`);
      setPayments(payments.map(payment => 
        payment._id === paymentId 
          ? { ...payment, status: 'completed' } 
          : payment
      ));
      fetchPaymentStats(); // Refresh stats
    } catch (error) {
      setError('Failed to release escrow payment');
    }
  };

  const handleRefundPayment = async (paymentId) => {
    if (!window.confirm('Are you sure you want to refund this payment?')) {
      return;
    }

    try {
      await axios.put(`/api/v1/payments/${paymentId}/refund`);
      setPayments(payments.map(payment => 
        payment._id === paymentId 
          ? { ...payment, status: 'refunded' } 
          : payment
      ));
      fetchPaymentStats(); // Refresh stats
    } catch (error) {
      setError('Failed to refund payment');
    }
  };

  const handleWithdraw = async () => {
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      setError('Please enter a valid withdrawal amount');
      return;
    }

    if (parseFloat(withdrawAmount) > stats.availableBalance) {
      setError('Withdrawal amount exceeds available balance');
      return;
    }

    try {
      setWithdrawing(true);
      await axios.post('/api/v1/payments/withdraw', {
        amount: parseFloat(withdrawAmount)
      });
      
      setShowWithdrawModal(false);
      setWithdrawAmount('');
      fetchPaymentStats(); // Refresh stats
      fetchPayments(); // Refresh payments
    } catch (error) {
      setError('Failed to process withdrawal');
    } finally {
      setWithdrawing(false);
    }
  };

  const filterPayments = (payments, filter) => {
    switch (filter) {
      case 'completed':
        return payments.filter(p => p.status === 'completed');
      case 'pending':
        return payments.filter(p => p.status === 'pending');
      case 'escrow':
        return payments.filter(p => p.status === 'escrow');
      default:
        return payments;
    }
  };

  const filteredPayments = filterPayments(payments, activeTab);

  const tabs = [
    { key: 'all', label: 'All', count: payments.length },
    { key: 'completed', label: 'Completed', count: payments.filter(p => p.status === 'completed').length },
    { key: 'pending', label: 'Pending', count: payments.filter(p => p.status === 'pending').length },
    { key: 'escrow', label: 'In Escrow', count: payments.filter(p => p.status === 'escrow').length }
  ];

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <PaymentContainer>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
        <h1 style={{ fontSize: '2rem', fontWeight: '700', color: '#111827', margin: 0 }}>
          Payment Dashboard
        </h1>
        {user.role === 'freelancer' && stats.availableBalance > 0 && (
          <Button onClick={() => setShowWithdrawModal(true)}>
            Withdraw Funds
          </Button>
        )}
      </div>

      {error && (
        <Alert variant="error" style={{ marginBottom: '2rem' }}>
          {error}
        </Alert>
      )}

      {/* Stats Cards */}
      <StatsGrid>
        <StatCard>
          <StatNumber>${stats.totalEarnings}</StatNumber>
          <StatLabel>Total Earnings</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>${stats.pendingPayments}</StatNumber>
          <StatLabel>Pending Payments</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>${stats.escrowBalance}</StatNumber>
          <StatLabel>In Escrow</StatLabel>
        </StatCard>
        <StatCard>
          <StatNumber>${stats.availableBalance}</StatNumber>
          <StatLabel>Available Balance</StatLabel>
        </StatCard>
      </StatsGrid>

      {/* Escrow Information */}
      {stats.escrowBalance > 0 && (
        <EscrowSection>
          <EscrowTitle>Escrow Protection</EscrowTitle>
          <p style={{ margin: '0 0 0.5rem 0', fontSize: '0.875rem', color: '#166534' }}>
            Your payments are secured in escrow until work is completed and approved.
          </p>
          <EscrowAmount>${stats.escrowBalance} in escrow</EscrowAmount>
        </EscrowSection>
      )}

      {/* Payments List */}
      <Card noPadding style={{ marginTop: '2rem' }}>
        <div style={{ padding: '1.5rem 1.5rem 0 1.5rem' }}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#111827', margin: '0 0 1rem 0' }}>
            Payment History
          </h2>
          <Tabs
            tabs={tabs}
            activeTab={activeTab}
            onChange={setActiveTab}
          />
        </div>

        <div style={{ padding: '1.5rem' }}>
          {filteredPayments.length === 0 ? (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>
              <h3 style={{ margin: '0 0 1rem 0', color: '#374151' }}>
                No payments found
              </h3>
              <p style={{ margin: 0 }}>
                Payment transactions will appear here once you start working on projects.
              </p>
            </div>
          ) : (
            filteredPayments.map(payment => (
              <PaymentCard key={payment._id}>
                <PaymentHeader>
                  <div>
                    <PaymentAmount>${payment.amount}</PaymentAmount>
                    <Badge variant={getPaymentStatusColor(payment.status)}>
                      {payment.status}
                    </Badge>
                  </div>
                  <ActionButtons>
                    {payment.status === 'escrow' && user.role === 'employer' && (
                      <Button 
                        size="sm" 
                        onClick={() => handleReleaseEscrow(payment._id)}
                      >
                        Release Payment
                      </Button>
                    )}
                    {payment.status === 'completed' && user.role === 'employer' && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleRefundPayment(payment._id)}
                      >
                        Request Refund
                      </Button>
                    )}
                  </ActionButtons>
                </PaymentHeader>

                <PaymentMeta>
                  <span>{payment.contract?.title}</span>
                  <span>•</span>
                  <span>
                    {user.role === 'freelancer' 
                      ? `From: ${payment.employer?.firstName} ${payment.employer?.lastName}`
                      : `To: ${payment.freelancer?.firstName} ${payment.freelancer?.lastName}`
                    }
                  </span>
                  <span>•</span>
                  <span>{new Date(payment.createdAt).toLocaleDateString()}</span>
                </PaymentMeta>

                {payment.description && (
                  <PaymentDescription>{payment.description}</PaymentDescription>
                )}
              </PaymentCard>
            ))
          )}
        </div>
      </Card>

      {/* Withdrawal Modal */}
      <Modal
        isOpen={showWithdrawModal}
        onClose={() => setShowWithdrawModal(false)}
        title="Withdraw Funds"
      >
        <WithdrawalForm>
          <div>
            <p style={{ margin: '0 0 1rem 0', color: '#6b7280' }}>
              Available balance: <strong>${stats.availableBalance}</strong>
            </p>
            <Input
              label="Withdrawal Amount"
              type="number"
              placeholder="0.00"
              value={withdrawAmount}
              onChange={(e) => setWithdrawAmount(e.target.value)}
              max={stats.availableBalance}
            />
          </div>
          <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
            <Button 
              variant="secondary" 
              onClick={() => setShowWithdrawModal(false)}
            >
              Cancel
            </Button>
            <Button 
              onClick={handleWithdraw}
              loading={withdrawing}
              disabled={withdrawing}
            >
              Withdraw
            </Button>
          </div>
        </WithdrawalForm>
      </Modal>
    </PaymentContainer>
  );
};

export default PaymentDashboard;
