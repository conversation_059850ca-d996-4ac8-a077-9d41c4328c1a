import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const JobsContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
`;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2rem;
`;

const SearchBar = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
  
  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }
`;

const JobList = styled.div`
  display: grid;
  gap: 1.5rem;
`;

const JobCard = styled.div`
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
`;

const JobTitle = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
`;

const JobCompany = styled.p`
  color: #4b5563;
  margin-bottom: 1rem;
`;

const JobDescription = styled.p`
  color: #6b7280;
  margin-bottom: 1rem;
`;

const JobDetails = styled.div`
  display: flex;
  gap: 1rem;
  color: #4b5563;
  font-size: 0.875rem;
`;

const ApplyButton = styled.button`
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    background-color: #1d4ed8;
  }
  
  &:disabled {
    background-color: #93c5fd;
    cursor: not-allowed;
  }
`;

const Jobs = () => {
  const { user } = useAuth();
  const [jobs, setJobs] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        const response = await axios.get('/api/jobs');
        setJobs(response.data);
      } catch (error) {
        console.error('Error fetching jobs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, []);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleApply = async (jobId) => {
    try {
      await axios.post(`/api/jobs/${jobId}/apply`);
      // Update the job list to reflect the application
      setJobs(jobs.map(job =>
        job.id === jobId
          ? { ...job, hasApplied: true }
          : job
      ));
    } catch (error) {
      console.error('Error applying for job:', error);
    }
  };

  const filteredJobs = jobs.filter(job =>
    job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <JobsContainer>
      <Title>Available Jobs</Title>
      <SearchBar>
        <SearchInput
          type="text"
          placeholder="Search jobs..."
          value={searchTerm}
          onChange={handleSearch}
        />
      </SearchBar>
      <JobList>
        {filteredJobs.map(job => (
          <JobCard key={job.id}>
            <JobTitle>{job.title}</JobTitle>
            <JobCompany>{job.company}</JobCompany>
            <JobDescription>{job.description}</JobDescription>
            <JobDetails>
              <span>${job.salary}</span>
              <span>•</span>
              <span>{job.location}</span>
              <span>•</span>
              <span>{job.type}</span>
            </JobDetails>
            {user?.user_type === 'freelancer' && (
              <ApplyButton
                onClick={() => handleApply(job.id)}
                disabled={job.hasApplied}
              >
                {job.hasApplied ? 'Applied' : 'Apply Now'}
              </ApplyButton>
            )}
          </JobCard>
        ))}
      </JobList>
    </JobsContainer>
  );
};

export default Jobs; 