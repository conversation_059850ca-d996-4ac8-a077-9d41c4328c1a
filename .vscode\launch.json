{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Launch Backend", "skipFiles": ["/**"], "program": "${workspaceFolder}/backend/server.js", "env": {"NODE_ENV": "development"}, "outFiles": ["${workspaceFolder}/backend/**/*.js"]}, {"type": "chrome", "request": "launch", "name": "Launch Frontend", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}/frontend"}], "compounds": [{"name": "Full Stack: Backend + Frontend", "configurations": ["Launch Backend", "Launch Frontend"]}]}