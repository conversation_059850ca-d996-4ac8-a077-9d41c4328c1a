import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';

const InputContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const Label = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  
  ${props => props.required && css`
    &::after {
      content: ' *';
      color: #dc2626;
    }
  `}
`;

const InputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const InputField = styled.input`
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827;
  background-color: white;
  transition: all 0.2s ease-in-out;

  &::placeholder {
    color: #9ca3af;
  }

  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  &:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
  }

  ${props => props.error && css`
    border-color: #dc2626;
    
    &:focus {
      border-color: #dc2626;
      box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }
  `}

  ${props => props.success && css`
    border-color: #059669;
    
    &:focus {
      border-color: #059669;
      box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
    }
  `}

  ${props => props.size === 'sm' && css`
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  `}

  ${props => props.size === 'lg' && css`
    padding: 1rem;
    font-size: 1rem;
  `}

  ${props => props.leftIcon && css`
    padding-left: 2.5rem;
  `}

  ${props => props.rightIcon && css`
    padding-right: 2.5rem;
  `}
`;

const IconContainer = styled.div`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  pointer-events: none;

  ${props => props.position === 'left' && css`
    left: 0.75rem;
  `}

  ${props => props.position === 'right' && css`
    right: 0.75rem;
  `}
`;

const HelperText = styled.div`
  font-size: 0.75rem;
  color: #6b7280;

  ${props => props.error && css`
    color: #dc2626;
  `}

  ${props => props.success && css`
    color: #059669;
  `}
`;

const Input = forwardRef(({ 
  label, 
  error, 
  success,
  helperText, 
  leftIcon, 
  rightIcon, 
  required,
  className,
  size = 'md',
  ...props 
}, ref) => {
  return (
    <InputContainer className={className}>
      {label && (
        <Label htmlFor={props.id} required={required}>
          {label}
        </Label>
      )}
      
      <InputWrapper>
        {leftIcon && (
          <IconContainer position="left">
            {leftIcon}
          </IconContainer>
        )}
        
        <InputField
          ref={ref}
          error={error}
          success={success}
          leftIcon={leftIcon}
          rightIcon={rightIcon}
          size={size}
          {...props}
        />
        
        {rightIcon && (
          <IconContainer position="right">
            {rightIcon}
          </IconContainer>
        )}
      </InputWrapper>
      
      {(helperText || error) && (
        <HelperText error={!!error} success={success}>
          {error || helperText}
        </HelperText>
      )}
    </InputContainer>
  );
});

Input.displayName = 'Input';

export default Input;
